import { useState } from "react";
import {
  MdKeyboardArrowLeft,
  MdOutlineKeyboardDoubleArrowLeft,
} from "react-icons/md";
import { Link, NavLink, useLocation } from "react-router-dom";
import { IoMdAnalytics, IoMdHome } from "react-icons/io";
import { FaStore } from "react-icons/fa";
import { IoBagCheck } from "react-icons/io5";
import { FaUserGroup } from "react-icons/fa6";
import { BiSolidMessage, BiX } from "react-icons/bi";
import { Button, Tooltip } from "@heroui/react";
import { GrUpgrade } from "react-icons/gr";

interface SidebarProps {
  onLinkClick?: () => void;
}

const Sidebar = ({ onLinkClick }: SidebarProps) => {
  const [foldSidebar, setFoldSidebar] = useState(false);
  const [expandedItem, setExpandedItem] = useState<number | null>(null);
  const { pathname } = useLocation();
  console.log(pathname);

  function isActive(to: string) {
    return pathname.replace("/admin/", "") === to ? true : false;
  }

  const handleLinkClick = () => {
    if (onLinkClick) {
      onLinkClick();
    }
  };

  const items = [
    {
      type: "navlink",
      to: "/",
      title: "Home",
      icon: <IoMdHome className="text-lg" />,
    },
    {
      type: "collapsible",
      title: "Analytics",
      icon: <IoMdAnalytics className="text-lg" />,
      children: [
        { to: "/analytics/traffic-overview", title: "Traffic Overview" },
        { to: "/analytics/real-time-overview", title: "Real-time Overview" },
        { to: "/analytics/sales-overview", title: "Sales Overview" },
        { to: "/analytics/marketing-overview", title: "Marketing Overview" },
        { to: "/analytics/behavior-overview", title: "Behavior Overview" },
      ],
    },
    {
      type: "collapsible",
      to: "/store",
      title: "Store",
      icon: <FaStore className="text-lg" />,
      children: [
        { to: "store/products", title: "Products" },
        { to: "store/inventory", title: "Inventory" },
        { to: "store/collections", title: "Collections" },
        { to: "store/website-menu", title: "Website Menu" },
      ],
    },
    {
      type: "collapsible",
      title: "Sales",
      to: "/sales",
      icon: <IoBagCheck className="text-lg" />,
      children: [
        { to: "sales/orders", title: "Orders" },
        { to: "sales/payments", title: "Payments" },
        { to: "sales/invoice", title: "Invoice" },
      ],
    },
    {
      type: "collapsible",
      title: "Customers & Leads",
      icon: <FaUserGroup className="text-lg" />,
      children: [
        { to: "customers-leads/customers", title: "Customers" },
        {
          to: "/customers-leads/contact-form",
          title: "Contact Form Submission",
        },
        {
          to: "/customers-leads/back-in-stock",
          title: "Back-in-stock Request",
        },
      ],
    },
    {
      type: "navlink",
      to: "/",
      title: "Inbox",
      icon: <BiSolidMessage className="text-lg" />,
    },
  ];

  const handleItemClick = (index: number) => {
    setExpandedItem(expandedItem === index ? null : index);
  };

  return (
    <aside
      className={`${
        foldSidebar ? "w-16 px-1 pt-1" : "w-60 p-2"
      } transition-all duration-500 ease-in dark:bg-gray-900 h-[calc(100vh-50px)] dark:text-white flex bg-white flex-col justify-between relative text-small border-r border-[#dfe5eb] dark:border-r-gray-800 shadow-lg lg:shadow-none`}
    >
      <div>
        <div className="flex justify-between items-center mb-2">
          <div className="lg:hidden p-2">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={handleLinkClick}
              className="text-gray-500"
            >
              <BiX size={24} />
            </Button>
          </div>
          <Tooltip size="sm" content="Collapse" className="hidden lg:block">
            <button
              type="button"
              className="hidden lg:flex items-center justify-end px-1.5 hover:cursor-pointer hover:-right-8 transition-all duration-150 ease-in -z-10 w-10 h-6 text-black hover:text-primary border dark:border-slate-900 hover:bg-slate-100 hover:dark:bg-slate-700 dark:bg-[#42454c] dark:text-white absolute -right-6 rounded-xl mt-5"
              onClick={() => {
                setExpandedItem(null);
                setFoldSidebar(!foldSidebar);
              }}
            >
              <MdOutlineKeyboardDoubleArrowLeft />
            </button>
          </Tooltip>
        </div>

        <div className="w-full px-2 mt-5">
          {items.map((item, index) => (
            <div
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              key={index}
              className="mb-1 w-full transition-all duration-300 ease-in"
            >
              {item.type === "navlink" && item.to ? (
                <NavLink
                  to={item.to}
                  className="flex items-center justify-between w-full py-2 px-2 rounded hover:bg-lightPrimary hover:text-textStandard hover:dark:text-white hover:dark:bg-slate-700 cursor-pointer"
                  onClick={handleLinkClick}
                >
                  <div
                    className={`flex items-center w-full ${
                      foldSidebar ? "justify-center" : ""
                    }`}
                  >
                    {item.icon}
                    <span className={`ml-2 ${foldSidebar ? "hidden" : ""}`}>
                      {item.title}
                    </span>
                  </div>
                </NavLink>
              ) : (
                <>
                  <button
                    type="button"
                    className={`flex items-center justify-between text-start w-full py-2 px-2 hover:rounded ${
                      expandedItem === index
                        ? "hover:bg-lightPrimary hover:dark:bg-slate-700 bg-lightPrimary  hover:text-textStandard hover:dark:text-white dark:bg-[#42454c] rounded-t"
                        : "bg-transparent"
                    } hover:bg-lightPrimary hover:dark:bg-slate-700 cursor-pointer hover:text-primary hover:dark:text-white`}
                    onClick={() => {
                      if (foldSidebar) {
                        setFoldSidebar(false);
                      }
                      handleItemClick(index);
                    }}
                  >
                    <div
                      className={`flex items-center w-full ${
                        foldSidebar ? "justify-center" : ""
                      }`}
                    >
                      {item.icon}
                      <span className={`ml-2 ${foldSidebar ? "hidden" : ""}`}>
                        {item.title}
                      </span>
                    </div>
                    <MdKeyboardArrowLeft
                      className={`transform ${
                        expandedItem === index ? "rotate-90" : ""
                      } ${foldSidebar ? "hidden" : ""}`}
                    />
                  </button>

                  <ul
                    className={` bg-lightPrimary dark:bg-[#42454c] rounded-b transition-all duration-400 ease-in list-disc ${
                      expandedItem === index
                        ? "block w-full"
                        : "w-0 h-0 overflow-hidden hidden"
                    } py-2`}
                  >
                    {item.children?.map((child, childIndex) => (
                      <Link
                        to={child.to}
                        // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                        key={childIndex}
                        className={`py-2 mx-2 rounded group hover:text-primary hover:dark:text-white px-4 gap-3 text-tiny flex items-center hover:bg-[#D4EBFD] hover:dark:bg-slate-700 hover:cursor-pointer ${
                          isActive(child.to)
                            ? "bg-[#D4EBFD] dark:bg-slate-700 text-primary"
                            : ""
                        }`}
                        onClick={handleLinkClick}
                      >
                        <div className="flex items-center gap-2">
                          <span
                            className={`h-1.5 w-1.5  dark:bg-white rounded-full group-hover:bg-primary transition-all duration-150 ease-in ${
                              isActive(child.to) ? "bg-primary" : "bg-gray-300"
                            }`}
                          >
                            &nbsp;
                          </span>
                          <span>{child.title}</span>
                        </div>
                      </Link>
                    ))}
                  </ul>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="w-full flex flex-col gap-3 mb-2">
        <div className="bg-[#0000000d] py-3 px-2 flex flex-col items-center gap-2 rounded-md mb-2">
          <GrUpgrade
            className={`text-center text-xl font-bold ${
              foldSidebar ? "text-primary" : ""
            }`}
          />
          <div className="text-tiny" hidden={foldSidebar}>
            Ready to go beyond basic plan Upgrade for premium features
          </div>
          {!foldSidebar ? (
            <Button radius="sm" size="sm" className="w-full" color="primary">
              View Plans
            </Button>
          ) : null}
        </div>
        <div className="text-start px-2" hidden={foldSidebar}>
          <p className="text-tiny">
            Developed by <span className="font-semibold">Zerror Studios</span>
          </p>
          <p className="text-tiny">Copyright &copy; 2024 | Version 1.01.000</p>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
