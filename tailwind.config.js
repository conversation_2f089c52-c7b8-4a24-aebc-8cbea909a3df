// tailwind.config.js
import { heroui } from "@heroui/react";

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      animation: {
        "spin-slow": "spin 3s linear infinite",
      },
      colors: {
        textStandardPrimary: "#000624",
        textStandardSecondary: "#44485F",
        textStandardPrimaryLight: "#ffffff",
        textStandardSecondaryLight: "#868AA5",
        textStandard: "#3b82f6",
        textSuccess: "#25A55A",
        textError: "#eb0000",
        textWarning: "#FFB700",
        textPremium: "#9A27D5",
        textDisabled: "#BEBEBE",
        textDisabledLight: "##ffffffb3",
        textPlaceHolder: "#868AA5",
        textPlaceHolderLight: "#868AA5",
        expandedBackground: "#3e3e3e",
        lightPrimary: "#e6f4ff",
        grayBackground: "#413e3e",
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      prefix: "heroui", // prefix for themes variables
      addCommonColors: false, // override common colors (e.g. "blue", "green", "pink").
      defaultTheme: "light", // default theme from the themes object
      defaultExtendTheme: "light", // default theme to exten on custom themes
      layout: {
        radius: {
          small: "5px",
          medium: "50px",
          large: "10px",
        },
        fontSize: {
          medium: "16px",
          large: "24px",
          small: "14px",
          tiny: "11px",
        },
      },
      // common layout tokens (applied to all themes)
      themes: {
        light: {
          layout: {}, // light theme layout tokens
          colors: {
            focus: "#e6f4ff",
            primary: {
              DEFAULT: "#3b82f6",
              foreground: "#fff",
            },
            danger: {
              DEFAULT: "#eb0000",
              foreground: "#fff",
            },
            secondary: {
              DEFAULT: "#44485F",
            },
            warning: {
              DEFAULT: "#FFB700",
              foreground: "#fff",
            },
            success: {
              DEFAULT: "#25A55A",
              foreground: "#fff",
            },
          }, // light theme colors
        },
        dark: {
          layout: {}, // dark theme layout tokens
          colors: {
            primary: {
              DEFAULT: "#29394E ",
              foreground: "#fff",
            },
            danger: {
              DEFAULT: "#eb0000",
              foreground: "#fff",
            },
            secondary: {
              DEFAULT: "#44485F",
            },
            warning: {
              DEFAULT: "#FFB700",
              foreground: "#fff",
            },
            success: {
              DEFAULT: "#25A55A",
              foreground: "#fff",
            },
          }, // dark theme colors
        },
        // ... custom themes
      },
    }),
  ],
};
