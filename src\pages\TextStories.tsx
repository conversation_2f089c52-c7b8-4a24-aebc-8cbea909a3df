import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Input,
  Button,
  DropdownTrigger,
  Dropdown,
  DropdownMenu,
  Dropdown<PERSON>tem,
  <PERSON>r,
  Spinner,
  <PERSON><PERSON>,
  <PERSON>b,
  <PERSON>lt<PERSON>,
} from "@heroui/react";
import { useInfiniteScroll } from "@heroui/use-infinite-scroll";
import { useAsyncList } from "@react-stately/data";
import { useCallback, useMemo, useState } from "react";
import {
  BiChevronDown,
  BiExport,
  BiFilter,
  BiPlus,
  BiSearch,
} from "react-icons/bi";
import { FiEye } from "react-icons/fi";
import { LuPencil } from "react-icons/lu";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import {
  Drawer,
  <PERSON>er<PERSON>ontent,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON>,
  DrawerFooter,
  useDisclosure,
} from "@heroui/react";
import { RxDividerVertical } from "react-icons/rx";
import { CgTrashEmpty } from "react-icons/cg";

interface ColumnType {
  name: string;
  uid: string;
  sortable?: boolean;
}

export const columns: ColumnType[] = [
  { name: "ID", uid: "id", sortable: false },
  { name: "NAME", uid: "name", sortable: false },
  { name: "Height", uid: "height", sortable: false },
  { name: "Mass", uid: "mass", sortable: false },
  { name: "Hair Color", uid: "hair_color" },
  { name: "STATUS", uid: "status", sortable: false },
  { name: "ACTIONS", uid: "actions" },
];

enum StatusColorsType {
  Active = "active",
  Paused = "paused",
  Vacation = "vacation",
}

interface StatusType {
  name: string;
  uid: StatusColorsType;
}

export const statusOptions: StatusType[] = [
  { name: "Active", uid: StatusColorsType.Active },
  { name: "Paused", uid: StatusColorsType.Paused },
  { name: "Vacation", uid: StatusColorsType.Vacation },
];

function capitalize(s: string): string {
  return s ? s.charAt(0).toUpperCase() + s.slice(1).toLowerCase() : "";
}

const INITIAL_VISIBLE_COLUMNS = [
  "name",
  "height",
  "mass",
  "hair_color",
  "actions",
];

interface UserType {
  id: string;
  name: string;
  height: string;
  mass: string;
  hair_color: string;
  status: StatusColorsType;
  avatar?: string;
  email?: string;
  role?: string;
  team?: string;
}

const TextStories = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedTab, setSelectedTab] = useState<string>("published");
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [filterValue, setFilterValue] = useState<string>("");
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set([]));
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(
    new Set(INITIAL_VISIBLE_COLUMNS)
  );
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const hasSearchFilter = Boolean(filterValue);

  const list = useAsyncList<UserType>({
    async load({ signal, cursor }) {
      if (cursor) {
        setIsLoading(false);
      }

      const res = await fetch(
        cursor || `https://swapi.py4e.com/api/people/?search=${filterValue}`,
        { signal }
      );
      const json = await res.json();

      setHasMore(json.next !== null);

      return {
        items: json.results,
        cursor: json.next,
      };
    },
  });

  const [loaderRef, scrollerRef] = useInfiniteScroll({
    hasMore,
    onLoadMore: list.loadMore,
  });

  const headerColumns = useMemo(() => {
    if (visibleColumns.has("all")) return columns;

    return columns.filter((column) =>
      Array.from(visibleColumns).includes(column.uid)
    );
  }, [visibleColumns]);

  const renderCell = useCallback((user: UserType, columnKey: string) => {
    const cellValue = user[columnKey as keyof UserType];

    switch (columnKey) {
      case "name":
        return (
          <User
            avatarProps={{ radius: "lg", src: user.avatar }}
            description={user.email}
            name={cellValue as string}
          >
            {user.email}
          </User>
        );
      case "role":
        return (
          <div className="flex flex-col">
            <p className="text-bold text-small capitalize">{cellValue}</p>
            <p className="text-bold text-tiny capitalize text-default-400">
              {user.team}
            </p>
          </div>
        );

      case "actions":
        return (
          <div className="relative flex items-center justify-center gap-3 z-50">
            <Tooltip content="Details" color="secondary">
              <span className="text-base p-1.5 bg-lightPrimary hover:bg-primary group rounded-full text-primary cursor-pointer active:opacity-50">
                <FiEye
                  className="group-hover:text-white"
                  onClick={() => console.log("clicked on the eye")}
                />
              </span>
            </Tooltip>
            <Tooltip content="Edit user" color="secondary">
              <span className="text-base p-1.5 bg-lightPrimary rounded-full hover:bg-primary group  cursor-pointer active:opacity-50">
                <LuPencil className="text-primary group-hover:text-white" />
              </span>
            </Tooltip>
            <Tooltip content="Delete user" color="secondary">
              <span className="text-base text-danger p-1.5 bg-lightPrimary hover:bg-primary group rounded-full cursor-pointer active:opacity-50">
                <CgTrashEmpty className="text-primary text-xl group-hover:text-white" />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const onSearchChange = useCallback(
    (value: string) => {
      if (value) {
        setFilterValue(value);
        list.reload();
      } else {
        setFilterValue("");
        list.reload();
      }
    },
    [list]
  );

  const onClear = useCallback(() => {
    setFilterValue("");
    list.reload();
  }, [list]);

  const topContent = useMemo(() => {
    return (
      <div className="flex flex-col gap-4 bg-white dark:bg-slate-900 p-2 rounded-md">
        <div className="flex justify-between gap-3 items-center ">
          <div>
            <Tabs
              aria-label="Tabs variants"
              variant={"underlined"}
              selectedKey={selectedTab}
              onSelectionChange={(key) => setSelectedTab(key.toString())}
              classNames={{
                base: "w-full",
                panel: "active:outline-none",
                tab: "text-sm",
                tabList: "flex gap-2",
                tabContent:
                  "group-data-[selected=true]:text-primary group-data-[selected=true]:font-semibold",
                cursor: "w-full bg-primary",
              }}
            >
              <Tab key="all" title="All (25)" />
              <Tab key="published" title="Published (10)" />
              <Tab key="unpublished" title="Unpublished (20)" />
              <Tab key="draft" title="Draft (05)" />
              <Tab key="scheduled" title="Scheduled (04)" />
            </Tabs>
          </div>
          <div className="flex gap-3">
            <Input
              isClearable
              size="sm"
              classNames={{
                base: "w-72 rounded-full",
              }}
              radius="full"
              placeholder="Search..."
              startContent={<BiSearch />}
              value={filterValue}
              onClear={onClear}
              onValueChange={onSearchChange}
            />
            <div className="flex gap-3">
              <Dropdown>
                <DropdownTrigger className="hidden sm:flex !rounded-[40px]">
                  <Button
                    endContent={<BiChevronDown className="text-small " />}
                    variant="ghost"
                    size="sm"
                    color="primary"
                    className="border"
                  >
                    Columns
                  </Button>
                </DropdownTrigger>
                <DropdownMenu
                  disallowEmptySelection
                  aria-label="Table Columns"
                  closeOnSelect={false}
                  selectedKeys={visibleColumns}
                  selectionMode="multiple"
                  onSelectionChange={(keys) => {
                    setVisibleColumns(keys as Set<string>);
                  }}
                >
                  {columns.map((column) => (
                    <DropdownItem key={column.uid} className="capitalize">
                      {capitalize(column.name)}
                    </DropdownItem>
                  ))}
                </DropdownMenu>
              </Dropdown>
              <Button
                color="primary"
                className="border"
                onPress={onOpen}
                size="sm"
                variant="ghost"
                radius="full"
                endContent={<BiFilter />}
              >
                Filters
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }, [
    filterValue,
    visibleColumns,
    list.items.length,
    onSearchChange,
    hasSearchFilter,
  ]);

  const topSelectedContent = () => {
    return (
      <div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 px-2 py-3 rounded-md">
        <p>10 of 10 Selected</p>
        <RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
        <div>
          <Button
            radius="full"
            color="primary"
            size="sm"
            variant="ghost"
            startContent={<BiExport />}
          >
            Export
          </Button>
        </div>
      </div>
    );
  };

  return (
    <>
      <div>
        <Breadcrumbs>
          <BreadcrumbItem>Home</BreadcrumbItem>
          <BreadcrumbItem>Products</BreadcrumbItem>
        </Breadcrumbs>
      </div>
      <div className="flex justify-between items-center w-full mb-5 mt-2">
        <h1>Products (10)</h1>
        <Button
          size="sm"
          radius="full"
          color="primary"
          startContent={<BiPlus />}
        >
          Add New Product
        </Button>
      </div>

      <Drawer isOpen={isOpen} backdrop="opaque" onOpenChange={onOpenChange}>
        <DrawerContent>
          {(onClose) => (
            <>
              <DrawerHeader className="flex flex-col gap-1 text-base">
                Custom Motion Drawer
              </DrawerHeader>
              <DrawerBody>
                <p className="text-small">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  Nullam pulvinar risus non risus hendrerit venenatis.
                  Pellentesque sit amet hendrerit risus, sed porttitor quam.
                </p>
              </DrawerBody>
              <DrawerFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Close
                </Button>
                <Button color="primary" onPress={onClose}>
                  Action
                </Button>
              </DrawerFooter>
            </>
          )}
        </DrawerContent>
      </Drawer>
      <Table
        // isVirtualized
        isHeaderSticky
        aria-label="Example table with infinite pagination"
        baseRef={scrollerRef}
        className="mainTable gap-0 border-1 border-gray-200 dark:border-slate-600 rounded-lg"
        bottomContent={
          hasMore ? (
            <div className="flex w-full justify-center">
              <Spinner ref={loaderRef} color="primary" />
            </div>
          ) : null
        }
        classNames={{
          base: "max-h-[600px] overflow-hidden",
          table: "min-h-[100px] ",
          wrapper: "scrollbar p-0 shadow-none rounded-none mt-0",
          thead: "!rounded-none ",
          th: "bg-[#c3e5ff] dark:bg-black py-3 !rounded-none font-semibold text-primaryBlack text-sm",
          tr: "hover:bg-lightPrimary dark:hover:bg-slate-600",
        }}
        selectedKeys={selectedKeys}
        onRowAction={() => {
          return;
        }}
        selectionBehavior="toggle"
        selectionMode="multiple"
        topContent={selectedKeys.size > 0 ? topSelectedContent() : topContent}
        topContentPlacement="outside"
        onSelectionChange={(keys) => {
          setSelectedKeys(keys as Set<string>);
        }}
      >
        <TableHeader columns={headerColumns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
              allowsSorting={column.sortable}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          isLoading={isLoading}
          items={list.items}
          emptyContent={"No users found"}
          // loadingContent={<Spinner color="primary" />}
        >
          {(item) => (
            <TableRow key={item.name}>
              {(columnKey) => {
                return (
                  <TableCell>{renderCell(item, columnKey as string)}</TableCell>
                );
              }}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
};
export default TextStories;
