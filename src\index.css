@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
	font-family: "wix-font";
	src: url("/fonts/font-wix.woff2") format("woff2");
}
@theme {
	--font-sans: "wix-font", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

:root {
	/* Theme colors */
	--text-standard-primary: "#000624";
	--text-standard-secondary: "#44485F";
	--tex-standard-primary-light: "#ffffff";
	--text-standard-secondary-light: "#868AA5";
	--text-primary: "#3b82f6";
	--text-success: "#25A55A";
	--text-error: "#eb0000";
	--text-warning: "#FFB700";
	--text-premium: "#9A27D5";
	--text-disabled: "#BEBEBE";
	--text-disabled-light: "#ffffffb3";
	--text-placeholder: "#868AA5";
	--text-placeholder-light: "#868AA5";
	--text-link: "#116DFF";

	/* Font Weights */
	--font-weight-regular: 400;
	--font-weight-medium: 500;
	--font-weight-bold: 700;

	/* Font Sizes */
	--font-size-extra-tiny: 11px; /* Extra Tiny Body Text */
	--font-size-tiny: 12px; /* Tiny Body Text, H6 - caption */
	--font-size-small: 14px; /* Small Body Text */
	--font-size-medium: 16px; /* Medium body text (default) */
	--font-size-h5: 15px; /* H5 - card section title */
	--font-size-h4: 18px; /* H4 - page section title */
	--font-size-h3: 20px; /* H3 - card title */
	--font-size-h2: 21px; /* H2 - modal title */
	--font-size-h1: 28px; /* H1 - page title */

	/* Line Heights */
	--line-height-extra-tiny: 16px; /* height-100: Extra tiny body text */
	--line-height-tiny: 20px; /* height-200: Tiny body text */
	--line-height-h4: 24px; /* height-300: H4 - page section title */
	--line-height-h2: 28px; /* height-400: H2 - modal title */
	--line-height-h1: 32px; /* height-500: H1 - page title */
}

html,
body {
	color: var(--text-standard-primary);
	font-size: var(--font-size-medium);
	line-height: calc(var(--font-size-medium) * 1.5); /* 1.5 times the font size */
	font-family: "wix-font", sans-serif;
	font-weight: var(--font-weight-regular);
	@media (prefers-color-scheme: dark) {
		color-scheme: dark;
		color: var(--text-standard-primary-light);
	}
}

/* Headings */
h1 {
	font-size: var(--font-size-h1);
	line-height: var(--line-height-h1);
	font-weight: var(--font-weight-bold);
}

h2 {
	font-size: var(--font-size-h2);
	line-height: var(--line-height-h2);
	font-weight: var(--font-weight-bold);
}

h3 {
	font-size: var(--font-size-h3);
	line-height: var(--line-height-h2); /* Assuming same line-height as H2 */
	font-weight: var(--font-weight-bold);
}

h4 {
	font-size: var(--font-size-h4);
	line-height: var(--line-height-h4);
	font-weight: var(--font-weight-medium);
}

h5 {
	font-size: var(--font-size-h5);
	line-height: var(--line-height-h4); /* Assuming same line-height as H4 */
	font-weight: var(--font-weight-medium);
}

h6 {
	font-size: var(--font-size-tiny);
	line-height: var(--line-height-tiny);
	font-weight: var(--font-weight-regular);
}

/* Paragraph */
p {
	font-size: var(--font-size-medium);
	line-height: calc(var(--font-size-medium) * 1.5); /* 1.5 times the font size */
	font-weight: var(--font-weight-regular);
}

/* Section Title */
.section-title {
	font-size: var(--font-size-h4);
	line-height: var(--line-height-h4);
	font-weight: var(--font-weight-bold);
}

/* Text */
.text-extra-tiny {
	font-size: var(--font-size-extra-tiny);
	line-height: var(--line-height-extra-tiny);
	font-weight: var(--font-weight-regular);
}

.text-tiny {
	font-size: var(--font-size-tiny);
	line-height: var(--line-height-tiny);
	font-weight: var(--font-weight-regular);
}

.text-small {
	font-size: var(--font-size-small);
	line-height: calc(var(--font-size-small) * 1.5); /* 1.5 times the font size */
	font-weight: var(--font-weight-regular);
}

.text-medium {
	font-size: var(--font-size-medium);
	line-height: calc(var(--font-size-medium) * 1.5); /* 1.5 times the font size */
	font-weight: var(--font-weight-regular);
}

a {
	color: var(--text-link);
}

/* Hide scrollbar for Chrome, Safari, and Opera */
.scrollbar::-webkit-scrollbar {
	display: none;
}

/* Hide scrollbar for IE, Edge, and Firefox */
.scrollbar {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

.mainTable label span,
.mainTable label span::before,
.mainTable label span::after,
.ordersTable label span,
.ordersTable label span::before,
.ordersTable label span::after,
.productOptionsTable label span,
.productOptionsTable label span::before,
.productOptionsTable label span::after,
.checkboxMenu span,
.checkboxMenu span:before,
.checkboxMenu span:after {
	border-radius: 3px;
	border-color: #3b82f6;
	height: 15px;
	width: 15px;
}

/* Product and inventory tables need wider second column */
.mainTable thead th:nth-child(2) {
	@apply min-w-[15rem] lg:min-w-[25rem] 2xl:min-w-[30rem];
}

/* Payments and orders tables don't need as wide columns */
.paymentsTable label span,
.paymentsTable label span::before,
.paymentsTable label span::after,
.ordersTable label span,
.ordersTable label span::before,
.ordersTable label span::after {
	border-radius: 3px;
	border-color: #3b82f6;
	height: 15px;
	width: 15px;
}
.before\:border-2::before {
	border-width: 1.2px !important;
}

/* Apply styles when the input is autofilled */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
	-webkit-box-shadow: 0 0 0 1000px #ffffff inset; /* Change to your desired background color */
	-webkit-text-fill-color: #000000; /* Change to your desired text color */
	transition: background-color 5000s ease-in-out 0s;
}

.absolute.inset-0.pointer-events-none.bg-transparent.overflow-hidden.rounded-medium {
	/* Your styles here */
	border-radius: 0 !important;
}

.boxShadow {
	box-shadow: 0 1px 1px #0000000a;
}
.listbox > div {
	-ms-overflow-style: none; /* IE and Edge */
	scrollbar-width: none; /* Firefox */
}

.tag-input {
	flex-wrap: wrap;
	border-radius: 5px;
}

.tags {
	display: flex;
	flex-wrap: wrap;
	list-style: none;
	padding: 0;
	margin: 0;
}

.tag {
	background-color: #e0e0e0;
	padding: 5px;
	margin: 2px;
	display: flex;
	align-items: center;
}

.tag-close {
	margin-left: 5px;
	cursor: pointer;
}

.input-tag {
	font-size: medium;
	font-weight: 200;
	width: 100%;
	border: none;
	border-radius: 5px;
	background-color: #f4f4f5;
	outline: none;
	flex: 1;
	padding: 5px;
}

.card-div {
	-webkit-backdrop-filter: blur(4px);
	backdrop-filter: blur(4px);
	background: #162d3d33 linear-gradient(180deg, #162c3c00, #162c3ccc);
	background-blend-mode: multiply;
	height: 100%;
	position: relative;
	width: 100%;
}

.card-items {
	background-color: #ffffffb3 !important;
	border-radius: 8px;
	box-shadow: var(--shadow-type1);
	cursor: pointer;
	overflow: hidden;
	width: calc(33.33333% - 30px);
}

.popover {
	position: absolute;
	z-index: 9999;
	top: -2rem;
	left: 4rem;
}

.cover {
	position: fixed;
	z-index: 9999;
}

#rc-editable-input-2 {
	color: white !important;
}

.chrome-picker {
	position: fixed;
	z-index: 90;
}

.css-13cymwt-control {
	background-color: #f0f4f8 !important;
	border: none !important;
}
.css-13cymwt-control:active,
.css-13cymwt-control:focus {
	border: none !important;
	outline: none !important;
}
.css-t3ipsp-control:hover {
	border: none !important;
	outline: none !important;
	border-color: transparent !important;
}
