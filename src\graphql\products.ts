import { gql } from "@apollo/client";

export const GET_PRODUCTS =
	gql(`query GetProducts($offset: Int, $limit: Int, $filters: GetProductsFilterInput) {
  getProducts(offset: $offset, limit: $limit, filters: $filters) {
    products {
      _id
      name
      description
      assets {
        _id
        path
        type
        isFeatured
        altText
        createdAt
        updatedAt
      }
      categoryIds
      categories {
        _id
        name
        imgsrc
        isDeleted
        createdAt
        updatedAt
      }
      price
      isOnSale
      saleType
      saleValue
      discountedPrice
      costOfGoods
      profit
      totalProductQuantity
      variants {
        selectedOptions
        variantPrice
        sku
        status
        visibility
      }
      allowCustomText
      customTexts {
        title
        charLimit
        isRequired
      }
      trackInventory
      stockQuantity
      stockStatus
      status
      isDeleted
      isOnSale
      discountedPrice
      createdAt
      updatedAt
      productOptions {
        optionName
        showInProductPageAs
        choices {
          name
        }
      }
    }
    totalCount
  }
}
`);

export const DELETE_PRODUCT = gql(`
  mutation DeleteProduct($deleteProductId: ID!) {
    deleteProduct(id: $deleteProductId)
  }
`);

export const UPDATE_PRODUCT_VISIBILITY =
	gql(`mutation UpdateProductVisibility($productId: String!, $showInOnlineStore: Boolean!) {
  updateProductVisibility(productId: $productId, showInOnlineStore: $showInOnlineStore)
}`);

export const BULK_UPDATE_PRODUCTS =
	gql(`mutation BulkUpdateProduct($productIds: [ID]!, $input: BulkUpdateProductInput) {
  bulkUpdateProduct(productIds: $productIds, input: $input)
}`);

export const BULK_DELETE_PRODUCTS = gql(`mutation BulkDeleteProducts($productIds: [ID]!) {
  bulkDeleteProducts(productIds: $productIds)
}`);

export const CREATE_PRODUCT = gql(`mutation CreateProduct($input: ProductInput!) {
  createProduct(input: $input) {
    _id
  }
}`);

export const GET_PRODUCT_BY_ID = gql(`
  query GetProductById($getProductByIdId: ID!) {
  getProductById(id: $getProductByIdId) {
    _id
    name
    description
    ribbon {
      ribbonId
      name
      # isDefault
      # isDeleted
      
    }
    assets {
      _id
      path
      type
      isFeatured
      altText
      createdAt
      updatedAt
    }
    categoryIds
    categories {
      _id
      name
      imgsrc
      isDeleted
    }
    price
    isOnSale
    saleType
    saleValue
    discountedPrice
    costOfGoods
    profit
    margin
    showPricePerUnit
    totalProductQuantity
    baseUnits
    productType
    digitalFile {
      fileName
      filePath
      fileType
      fileSize
    }
    productOptions {
      optionName
      showInProductPageAs
      choices {
        name
        images
      }
    }
    manageVariantQtyAndPrice
    variants {
      selectedOptions
      priceDifference
      variantPrice
      sku
      variantCostOfGoods
      shippingWeight
      trackInventory
      stockQuantity
      status
      stockStatus
      visibility
    }
    allowCustomText
    customTexts {
      title
      charLimit
      isRequired
    }
    trackInventory
    stockQuantity
    stockStatus
    additionalInfo {
      title
      description
    }
    status
    isDeleted
    
  }
}`);

export const UPDATE_PRODUCT = gql(`
 mutation UpdateProduct($productId: ID!, $input: UpdateProductInput) {
  updateProduct(productId: $productId, input: $input) {
    _id
  }
}
`);

export const BULK_DUPLICATE_PRODUCTS = gql(`
  mutation BulkDuplicateProducts($productIds: [ID]!) {
  bulkDuplicateProducts(productIds: $productIds) {
    _id
  }
}`);
