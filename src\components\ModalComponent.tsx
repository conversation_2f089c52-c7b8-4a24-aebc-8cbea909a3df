import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";
import { ButtonProperties } from "../types/buttonType";

type ModalPropTypes = {
  isOpen: boolean;
  onOpenChange: () => void;
  children: React.ReactNode;
  modalHeader?: string | null;
  closeButtonText?: string | null;
  saveButtonText?: string | null;
  className?: {
    closeColor?: ButtonProperties["color"];
    saveColor?: ButtonProperties["color"];
  };
  onPress: () => void;
  id?: string | undefined;
  size?: ButtonProperties["size"];
  isLoading?: boolean;
  disabled?: boolean;
  subheading?: string | null;
  onModalClose?: () => void;
  isDismissable?: boolean; //
};
const ModalComponent = ({
  isOpen,
  onOpenChange,
  children,
  closeButtonText = "Cancel",
  saveButtonText = "Save",
  modalHeader = null,
  className = {
    closeColor: "danger",
    saveColor: "primary",
  },
  onPress,
  id = undefined,
  size = undefined,
  isLoading = false,
  disabled = false,
  subheading = null,
  isDismissable = true,
  onModalClose,
}: ModalPropTypes) => {
  return (
    <Modal
      isOpen={isOpen}
      id={id}
      size={size}
      onOpenChange={onOpenChange}
      isDismissable={isDismissable}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              {modalHeader}
              {subheading ? (
                <div className="text-sm font-normal">{subheading}</div>
              ) : null}
            </ModalHeader>
            <ModalBody>{children}</ModalBody>
            <ModalFooter>
              <Button
                size="sm"
                radius="full"
                color={className.closeColor ? className.closeColor : undefined}
                variant="light"
                onPress={onModalClose ? onModalClose : onClose}
              >
                {closeButtonText}
              </Button>
              <Button
                size="sm"
                disabled={disabled}
                radius="full"
                className="disabled:cursor-not-allowed disabled:bg-default"
                isLoading={isLoading ? isLoading : false}
                color={className.saveColor ? className.saveColor : undefined}
                onPress={onPress}
              >
                {saveButtonText}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};

export default ModalComponent;
