import { useDisclosure } from "@heroui/react";

/**
 * Custom hook to manage all modals in the Products page
 */
export const useProductModals = () => {
  // Main modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure({
    id: "modal-1",
  });

  // Delete confirmation modal
  const {
    isOpen: isConfirmationOpen,
    onOpen: onCofirmatiionOpen,
    onClose: onConfirmationClose,
    onOpenChange: onConfirmationOpenChange,
  } = useDisclosure({ id: "modal-2" });

  // Product visibility modal
  const {
    isOpen: isVisibilityOpen,
    onOpen: onVisibilityOpen,
    onOpenChange: onVisibilityOpenChange,
    onClose: onVisibilityClose,
  } = useDisclosure({ id: "modal-3" });

  // Product visibility hidden modal
  const {
    isOpen: isVisibilityOpenHidden,
    onOpen: onVisibiltuOpenHidden,
    onOpenChange: onVisibilityOpenHiddenChange,
    onClose: onVisibilityHiddenClose,
  } = useDisclosure({ id: "modal-4" });

  // Duplicate product modal
  const {
    isOpen: isDuplicateOpen,
    onOpen: onDuplicateOpen,
    onClose: onDuplicateClose,
    onOpenChange: onDuplicateChange,
  } = useDisclosure({ id: "modal-5" });

  // Change price modal
  const {
    isOpen: isChangePriceOpen,
    onOpen: onChangePriceOpen,
    onClose: onChangePriceClose,
    onOpenChange: onChangePriceChange,
  } = useDisclosure({ id: "modal-6" });

  // Ribbon edit modal
  const {
    isOpen: isRibbonEditOpen,
    onOpen: onRibbonEditOpen,
    onClose: onRibbonEditClose,
    onOpenChange: onRibbonEditChange,
  } = useDisclosure({ id: "modal-7" });

  // Bulk delete product modal
  const {
    isOpen: isBulkDeletedProductOpen,
    onOpen: onBulkDeleteProductOpen,
    onClose: onBulkDeleteProductClose,
    onOpenChange: onBulkDeleteProductChange,
  } = useDisclosure({ id: "modal-8" });

  return {
    // Main modal
    mainModal: {
      isOpen,
      onOpen,
      onOpenChange,
      onClose,
    },

    // Delete confirmation modal
    confirmationModal: {
      isOpen: isConfirmationOpen,
      onOpen: onCofirmatiionOpen,
      onClose: onConfirmationClose,
      onOpenChange: onConfirmationOpenChange,
    },

    // Product visibility modal
    visibilityModal: {
      isOpen: isVisibilityOpen,
      onOpen: onVisibilityOpen,
      onOpenChange: onVisibilityOpenChange,
      onClose: onVisibilityClose,
    },

    // Product visibility hidden modal
    visibilityHiddenModal: {
      isOpen: isVisibilityOpenHidden,
      onOpen: onVisibiltuOpenHidden,
      onOpenChange: onVisibilityOpenHiddenChange,
      onClose: onVisibilityHiddenClose,
    },

    // Duplicate product modal
    duplicateModal: {
      isOpen: isDuplicateOpen,
      onOpen: onDuplicateOpen,
      onClose: onDuplicateClose,
      onOpenChange: onDuplicateChange,
    },

    // Change price modal
    changePriceModal: {
      isOpen: isChangePriceOpen,
      onOpen: onChangePriceOpen,
      onClose: onChangePriceClose,
      onOpenChange: onChangePriceChange,
    },

    // Ribbon edit modal
    ribbonEditModal: {
      isOpen: isRibbonEditOpen,
      onOpen: onRibbonEditOpen,
      onClose: onRibbonEditClose,
      onOpenChange: onRibbonEditChange,
    },

    // Bulk delete product modal
    bulkDeleteModal: {
      isOpen: isBulkDeletedProductOpen,
      onOpen: onBulkDeleteProductOpen,
      onClose: onBulkDeleteProductClose,
      onOpenChange: onBulkDeleteProductChange,
    },
  };
};
