// hooks/useInfiniteQueryScroll.ts
import { useEffect, useRef } from "react";

interface UseInfiniteQueryScrollProps<T> {
  items: T[];
  totalCount: number;
  loading: boolean;
  fetchMore: (options: { variables: { offset: number } }) => void;
  scrollContainerRef: React.RefObject<HTMLDivElement | null>; // new: ref to scroll container
}

export const useInfiniteQueryScroll = <T>({
  items,
  totalCount,
  loading,
  fetchMore,
  scrollContainerRef,
}: UseInfiniteQueryScrollProps<T>) => {
  const observerRef = useRef<HTMLDivElement | null>(null);
  const hasMore = items.length < totalCount;

  useEffect(() => {
    if (!observerRef.current || !hasMore) return;
    if (!scrollContainerRef.current) return; // ensure the container is present

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loading) {
          fetchMore({ variables: { offset: items.length } });
        }
      },
      {
        root: scrollContainerRef.current, // observe relative to the scroll container
        threshold: 1.0,
      }
    );

    observer.observe(observerRef.current);

    return () => observer.disconnect();
  }, [items.length, hasMore, loading, scrollContainerRef, fetchMore]);

  return {
    observerRef,
    hasMore,
    loading,
  };
};
