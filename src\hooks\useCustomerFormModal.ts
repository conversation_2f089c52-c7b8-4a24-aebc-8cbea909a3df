import { useDisclosure } from "@heroui/react";
import { useState } from "react";
import { CustomerType } from "../types/customerType";

/**
 * Custom hook to manage the customer form modal
 */
export const useCustomerFormModal = () => {
  const [customerData, setCustomerData] = useState<CustomerType | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Customer form modal
  const {
    isOpen,
    onOpen,
    onClose,
    onOpenChange,
  } = useDisclosure();

  // Open modal for creating a new customer
  const openCreateModal = () => {
    setCustomerData(null);
    setIsEditMode(false);
    onOpen();
  };

  // Open modal for editing an existing customer
  const openEditModal = (customer: CustomerType) => {
    setCustomerData(customer);
    setIsEditMode(true);
    onOpen();
  };

  return {
    customerFormModal: {
      isOpen,
      onOpen,
      onClose,
      onOpenChange,
    },
    customerData,
    isEditMode,
    openCreateModal,
    openEditModal,
  };
};
