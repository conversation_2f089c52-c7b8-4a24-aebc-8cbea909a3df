export const Dropzone = ({
  onFileChange,
}: {
  onFileChange: (file: File) => void;
}) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      onFileChange(e.target.files[0]);
    }
  };

  return (
    <div
      className="border border-dashed border-blue-400 h-36 flex items-center justify-center rounded-md cursor-pointer hover:bg-blue-50 transition"
      onClick={() => document.getElementById("fileInput")?.click()}
    >
      <input
        id="fileInput"
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileChange}
      />
      <span className="text-gray-400 text-sm">
        Drag n drop some files here, or click to select files
      </span>
    </div>
  );
};
