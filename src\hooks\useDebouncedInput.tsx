import { useEffect, useState } from "react";
import useDebounce from "./useDebounce";

interface UseDebouncedInputOptions {
	delay?: number;
	initialValue?: string;
	onChange?: (value: string) => void;
}

const useDebouncedInput = (options: UseDebouncedInputOptions = {}) => {
	const { delay = 300, initialValue = "", onChange } = options;
	const [searchValue, setSearchValue] = useState<string>(initialValue);
	const debouncedSearchValue = useDebounce(searchValue, delay);

	// Call the onChange callback whenever the debounced value changes
	useEffect(() => {
		if (onChange) {
			onChange(debouncedSearchValue);
		}
	}, [debouncedSearchValue, onChange]);

	const handleSearchChange = (value: string) => setSearchValue(value);

	return {
		searchValue,
		debouncedSearchValue,
		handleSearchChange,
		setSearchValue,
	};
};

export default useDebouncedInput;
