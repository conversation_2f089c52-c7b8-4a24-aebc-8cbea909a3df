import { Input, NumberInput } from "@heroui/react";
import {
  Control,
  Controller,
  UseFormSetValue,
  useFieldArray,
  UseFormWatch,
} from "react-hook-form";
import { OrderSchema } from "../../types/orderType";
import { BiPlus, BiTrash } from "react-icons/bi";

type AddDiscountModalProps = {
  errors: any;
  control: Control<OrderSchema, any>;
  setValue: UseFormSetValue<OrderSchema>;
  watch: UseFormWatch<OrderSchema>;
};

const AddDiscountModal = ({
  errors,
  control,
  setValue,
  watch,
}: AddDiscountModalProps) => {
  // Use fieldArray to handle the dynamic array of discount options
  const { fields, append, remove } = useFieldArray({
    control,
    name: "discountOptions",
  });

  // Add a new discount option
  const handleAddDiscountOption = () => {
    append({ name: "", rate: 0 });
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      {fields.map((field, index) => (
        <div
          key={field.id}
          className="grid grid-cols-12 w-full place-items-center"
        >
          <div className="grid w-full grid-cols-2 col-span-10 gap-4">
            <Controller
              control={control}
              name={`discountOptions.${index}.name`}
              render={({ field }) => (
                <Input
                  {...field}
                  isRequired
                  label="Name"
                  labelPlacement="outside"
                  size="sm"
                  classNames={{
                    inputWrapper:
                      "w-full after:h-[1px] after:bg-primary rounded-md z-10",
                    label: "text-base",
                  }}
                  placeholder="e.g. Seasonal discount"
                  isInvalid={!!errors.discountOptions?.[index]?.name}
                  errorMessage={errors.discountOptions?.[index]?.name?.message}
                />
              )}
            />

            {/* Rate */}
            <Controller
              control={control}
              name={`discountOptions.${index}.rate`}
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <NumberInput
                  hideStepper
                  label="Rate"
                  size="sm"
                  ref={ref}
                  onValueChange={(value) => {
                    onChange(value); // Update form state
                    setValue(`discountOptions.${index}.rate`, value); // Manually update the form value if needed
                  }}
                  onBlur={onBlur} // Ensure validation runs on blur
                  value={value}
                  labelPlacement="outside"
                  startContent="₹"
                  errorMessage={errors.discountOptions?.[index]?.rate?.message}
                  isInvalid={!!errors.discountOptions?.[index]?.rate?.message}
                  classNames={{
                    inputWrapper:
                      "w-full after:h-[1px] after:bg-primary rounded-md",
                    label: "text-base",
                  }}
                />
              )}
            />
          </div>
          <div className="flex justify-end items-center w-full">
            <button
              type="button"
              className="text-red-500 hover:cursor-pointer"
              onClick={() => remove(index)}
              disabled={fields.length === 1}
            >
              <BiTrash className="text-2xl mt-4" />
            </button>
          </div>
        </div>
      ))}

      <button
        type="button"
        className="text-primary w-fit flex items-center"
        onClick={handleAddDiscountOption}
      >
        <BiPlus />
        <span>
          {" "}
          {watch("discountOptions")?.length > 0 ? "Add Another" : "Add"}{" "}
          Discount
        </span>
      </button>
    </div>
  );
};

export default AddDiscountModal;
