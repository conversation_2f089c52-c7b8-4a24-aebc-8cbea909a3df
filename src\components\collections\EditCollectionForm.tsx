import React, { useState } from "react";
import { IoMdTrash } from "react-icons/io";
import { TbZoomReplace } from "react-icons/tb";
import { Dropzone } from "../forms/Dropzone";
import { Form, Input } from "@heroui/react";
import { fileToBase64 } from "../../helpers/fileToBase64";

type CollectionType = {
  collectionName: string;
  collectionImg: File | null | string;
};
type CollectionEditType = {
  collectionData: CollectionType;
  setCollectionData: (collectionData: any) => void;
};
const EditCollectionForm = ({
  collectionData,
  setCollectionData,
}: CollectionEditType) => {
  const [hovered, setHovered] = useState(false);

  const handleImageChange = async (file: File) => {
    const base64 = await fileToBase64(file);

    setCollectionData((prev: CollectionType) => ({
      ...prev,
      collectionImg: base64, // <-- this is now a base64 string
      file,
    }));
  };

  const handleImageRemove = () => {
    setCollectionData((prev: CollectionType) => ({
      ...prev,
      collectionImg: null,
      file: null,
    }));
  };

  return (
    <Form>
      <Input
        label="Collection Name"
        size="sm"
        value={collectionData.collectionName}
        onValueChange={(value) =>
          setCollectionData((prev: CollectionType) => ({
            ...prev,
            collectionName: value,
          }))
        }
        isRequired
        labelPlacement="outside"
        classNames={{
          inputWrapper: "w-full h-10 after:h-[1px] after:bg-primary rounded-md",
        }}
        placeholder="Coats"
        errorMessage="Please enter a collection name"
      />
      <div className="w-full">
        <label className="block mb-1 text-sm font-medium">Category Image</label>

        {collectionData.collectionImg ? (
          <div
            className="relative mx-auto flex justify-center  rounded-md overflow-hidden cursor-pointer"
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
          >
            <img
              src={
                typeof collectionData.collectionImg === "string"
                  ? collectionData.collectionImg
                  : collectionData.collectionImg
                  ? URL.createObjectURL(collectionData.collectionImg)
                  : undefined
              }
              alt="preview"
              className=" mx-auto object-cover h-48"
            />
            {hovered && (
              <div className="absolute  inset-0 bg-black/50 flex justify-center items-center space-x-4">
                {/* Replace Icon */}
                <TbZoomReplace
                  className="w-6 h-6 text-white"
                  onClick={handleImageRemove}
                />
                {/* Delete Icon */}
                <button
                  type="button"
                  onClick={handleImageRemove}
                  className="text-white"
                >
                  <IoMdTrash className="w-6 h-6" onClick={handleImageRemove} />
                </button>
              </div>
            )}
          </div>
        ) : (
          <Dropzone onFileChange={handleImageChange} />
        )}
      </div>
    </Form>
  );
};

export default EditCollectionForm;
