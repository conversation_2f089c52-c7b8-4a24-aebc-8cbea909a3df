import { gql } from "@apollo/client";

export const GET_COLLECTIONS = gql(`
query GetCategories($limit: Int, $offset: Int) {
  getCategories(limit: $limit, offset: $offset) {
    categories {
      _id
      imgsrc
      name
    }
  totalCount
  }
}`);

export const UPDATE_COLLECTION =
  gql(`mutation UpdateCategory($updateCategoryId: ID!, $input: UpdateCategoryInput!) {
  updateCategory(id: $updateCategoryId, input: $input) {
    _id
    imgsrc
    name
  }
}`);

export const DELETE_COLLECTION = gql(`
mutation DeleteCategory($deleteCategoryId: ID!) {
  deleteCategory(id: $deleteCategoryId)
}
`);

export const CREATE_COLLECTION = gql(`
mutation CreateCategory($input: CreateCategoryInput!) {
  createCategory(input: $input) {
    _id
    imgsrc
    name
  }
}
`);