import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import {
  CustomText,
  GetProductsType,
  ProductOptions,
} from "../../types/productType";
import { ProductType, StatusTypes, StockStatus } from "../../types/commonTypes";

const INITIAL_VISIBLE_COLUMNS = [
  "name",
  "price",
  "stockStatus",
  "status",
  "actions",
];

export interface ProductSlice {
  customTextFields: CustomText[];
  allowCustomText: boolean;
  productVisibleColumns: Set<string>;
  hasMore: boolean;
  selectedKeys: Set<string> | "all";
  deleteProductId: string | null;
  changePriceInputs: {
    type: string | null;
    value: number | null;
  };
  selectedRibbonId: string | null;
  filterText: string;
  products: GetProductsType[];
  filters: {
    status: StatusTypes | null;
    stockStatus: StockStatus | null;
    productType: ProductType | null;
  };
  productOptionIndex: number | null;
  productOptions: ProductOptions;
  addCustomTextField: () => void;
  addAnotherCustomTextField: () => void;
  removeCustomTextField: (index: number) => void;
  setAllowCustomText: (value: boolean) => void;
  setProductVisibleColumns: (value: Set<string>) => void;
  setHasMore: (value: boolean) => void;
  setSelectedKeys: (value: Set<string> | "all") => void;
  setDeleteProductId: (value: string | null) => void;
  setChangePriceType: (value: string | null) => void;
  setChangePriceValue: (value: number | null) => void;
  setFilterText: (value: string) => void;
  setProducts: (value: GetProductsType[]) => void;
  setSelectedRibbonId: (value: string | null) => void;
  setFilters: (value: {
    status?: StatusTypes | null;
    stockStatus?: StockStatus | null;
    productType?: ProductType | null;
  }) => void;
  setProductOptions: (options: ProductOptions) => void;
  resetProductOptions: () => void;
  setCustomTextField: (value: CustomText[]) => void;
  setProductOptionIndex: (index: number | null) => void;
}

const initialState: Omit<
  ProductSlice,
  | "addCustomTextField"
  | "addAnotherCustomTextField"
  | "removeCustomTextField"
  | "setAllowCustomText"
  | "setProductVisibleColumns"
  | "setHasMore"
  | "setSelectedKeys"
  | "setDeleteProductId"
  | "setChangePriceType"
  | "setChangePriceValue"
  | "setFilterText"
  | "setProducts"
  | "setSelectedRibbonId"
  | "setNewRibbons"
  | "removeNewRibbons"
  | "setFilters"
  | "setProductOptions"
  | "resetProductOptions"
  | "setCustomTextField"
  | "setProductOptionIndex"
> = {
  customTextFields: [],
  allowCustomText: false,
  productVisibleColumns: new Set(INITIAL_VISIBLE_COLUMNS),
  hasMore: false,
  selectedKeys: new Set([]),
  deleteProductId: null,
  changePriceInputs: {
    type: null,
    value: null,
  },
  filterText: "",
  products: [],
  selectedRibbonId: null,
  filters: {
    status: null,
    stockStatus: null,
    productType: null,
  },
  productOptions: { optionName: "", choices: [], showInProductPageAs: "List" },
  productOptionIndex: null,
};

export const productSlice: StateCreator<
  ProductSlice,
  [],
  [["zustand/immer", never]]
> = immer((set) => ({
  ...initialState,
  addCustomTextField: () =>
    set((state) => {
      state.customTextFields.push({
        title: "",
        charLimit: 0,
        isRequired: false,
      });
      state.allowCustomText = true;
    }),
  addAnotherCustomTextField: () =>
    set((state) => {
      state.customTextFields.push({
        title: "",
        charLimit: 0,
        isRequired: false,
      });
    }),
  removeCustomTextField: (index) =>
    set((state) => {
      state.customTextFields.splice(index, 1);
      state.allowCustomText = state.customTextFields.length > 0;
    }),
  setAllowCustomText: (value) =>
    set((state) => {
      state.allowCustomText = value;
    }),
  setProductVisibleColumns: (value: Set<string>) =>
    set((state) => {
      state.productVisibleColumns = value;
    }),
  setHasMore: (value) =>
    set((state) => {
      state.hasMore = value;
    }),
  setSelectedKeys: (value) =>
    set((state) => {
      state.selectedKeys = value;
    }),
  setDeleteProductId: (value) =>
    set((state) => {
      state.deleteProductId = value;
    }),
  setChangePriceType: (value) =>
    set((state) => {
      state.changePriceInputs.type = value;
    }),
  setChangePriceValue: (value) =>
    set((state) => {
      state.changePriceInputs.value = value;
    }),
  setFilterText: (value) =>
    set((state) => {
      state.filterText = value;
    }),
  setProducts: (value: GetProductsType[]) =>
    set((state) => {
      state.products = value;
    }),
  setSelectedRibbonId: (value) =>
    set((state) => {
      state.selectedRibbonId = value;
    }),
  setFilters: (value: Partial<typeof initialState.filters>) =>
    set((state) => {
      state.filters = {
        ...state.filters,
        ...value,
      };
    }),
  setProductOptions: (options) =>
    set((state) => {
      state.productOptions = options;
    }),
  resetProductOptions: () =>
    set((state) => {
      state.productOptions = {
        optionName: "",
        choices: [],
        showInProductPageAs: "List",
      };
    }),
  setCustomTextField: (value) =>
    set((state) => {
      state.customTextFields = value;
    }),
  setProductOptionIndex: (index) =>
    set((state) => {
      state.productOptionIndex = index;
    }),
}));
