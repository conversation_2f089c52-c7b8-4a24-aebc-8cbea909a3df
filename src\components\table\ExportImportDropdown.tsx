import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/react";
import { CiExport } from "react-icons/ci";
import { ExportImportDropdownItem } from "./TopHeader";

export const ExportImportDropdown = ({ items }: { items: ExportImportDropdownItem[] }) => (
	<Dropdown>
		<DropdownTrigger className="hidden sm:flex !rounded-[40px]">
			<Button
				endContent={<CiExport className="text-small" />}
				variant="ghost"
				size="sm"
				isIconOnly
				color="primary"
				className="border"
			/>
		</DropdownTrigger>
		<DropdownMenu aria-label="Export/Import" closeOnSelect>
			{items.map(({ key, label, description, icon, onPress }) => (
				<DropdownItem
					key={key}
					onPress={onPress}
					className="hover:!text-primary hover:bg-lightPrimary hover:dark:bg-slate-700 hover:dark:text-white"
				>
					<div className="flex items-center gap-2 p-2">
						{icon}
						<div className="flex flex-col">
							<h5>{label}</h5>
							<small>{description}</small>
						</div>
					</div>
				</DropdownItem>
			))}
		</DropdownMenu>
	</Dropdown>
);
