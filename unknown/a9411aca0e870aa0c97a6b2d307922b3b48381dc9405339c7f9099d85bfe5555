import { gql } from "@apollo/client";

export const FETCH_RIBBONS =
  gql(`query GetRibbonsByFilter($limit: Int, $offset: Int, $filters: GetRibbonsFiltersInput) {
  getRibbonsByFilter(limit: $limit, offset: $offset, filters: $filters) {
    ribbons {
      _id
      name
      products {
        totalCount
      }
    }
    totalCount
  }
}`);

export const DELETE_RIBBON = gql(`mutation DeleteRibbon($deleteRibbonId: ID!) {
  deleteRibbon(id: $deleteRibbonId)
}`);

export const CREATE_RIBBON = gql(`mutation CreateRibbon($name: String!) {
  createRibbon(name: $name) {
    name
    _id
  }
}`);

export const UPDATE_RIBBON =
  gql(`mutation UpdateRibbon($updateRibbonId: ID!, $input: UpdateRibbonInput) {
  updateRibbon(id: $updateRibbonId, input: $input) {
    name
    _id
  }
}`);

