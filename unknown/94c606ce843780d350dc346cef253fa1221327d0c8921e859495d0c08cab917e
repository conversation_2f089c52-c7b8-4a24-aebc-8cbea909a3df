import { useMutation, useQuery } from "@apollo/client";
import {
  <PERSON><PERSON>,
  Divider,
  Input,
  Radio,
  RadioGroup,
  Spinner,
} from "@heroui/react";
import { useRef, useState } from "react";
import { useBoundStore } from "../../store/store";
import { Bi<PERSON>heck, BiPencil, BiPlus, BiTrash, BiX } from "react-icons/bi";
import { useMutationHandler } from "../../hooks/useMutationStatusHandler";
import { useInfiniteQueryScroll } from "../../hooks/useInfiniteQueryScroll";
import {
  CREATE_LABEL,
  DELETE_LABEL,
  GET_LABELS,
  UPDATE_LABEL,
} from "../../graphql/customers";

// Define types for labels
export interface LabelType {
  _id: string;
  name: string;
  customers?: {
    totalCount: number;
  };
}

const EditLabelsModal = () => {
  const [addNewLabel, setAddNewLabel] = useState(false);
  const [newLabelName, setNewLabelName] = useState("");
  const [editingLabelId, setEditingLabelId] = useState<string | null>(null);
  const [editingLabelName, setEditingLabelName] = useState("");

  // Get selectedLabelId from store
  const { selectedLabelId, setSelectedLabelId } = useBoundStore();

  // Fetch labels from API
  const { data, loading, fetchMore } = useQuery(GET_LABELS, {
    variables: { limit: 10, offset: 0, filters: {} },
    notifyOnNetworkStatusChange: true,
  });

  // Delete label mutation
  const [
    deleteLabel,
    {
      data: deleteLabelData,
      loading: deleteLabelLoading,
      error: deleteLabelError,
    },
  ] = useMutation(DELETE_LABEL, {
    refetchQueries: [GET_LABELS, "GetLabelsByFilter"],
  });

  // Create label mutation
  const [
    createLabel,
    {
      data: createLabelData,
      loading: createLabelLoading,
      error: createLabelError,
    },
  ] = useMutation(CREATE_LABEL, {
    refetchQueries: [GET_LABELS, "GetLabelsByFilter"],
  });

  // Update label mutation
  const [
    updateLabel,
    {
      data: updateLabelData,
      loading: updateLabelLoading,
      error: updateLabelError,
    },
  ] = useMutation(UPDATE_LABEL, {
    refetchQueries: [GET_LABELS, "GetLabelsByFilter"],
  });

  // Handle mutation responses
  useMutationHandler({
    data: deleteLabelData,
    loading: deleteLabelLoading,
    error: deleteLabelError,
    successMessage: "Label deleted successfully",
  });

  useMutationHandler({
    data: createLabelData,
    loading: createLabelLoading,
    error: createLabelError,
    successMessage: "Label created successfully",
  });

  useMutationHandler({
    data: updateLabelData,
    loading: updateLabelLoading,
    error: updateLabelError,
    successMessage: "Label updated successfully",
  });

  // Handle delete label
  const handleDeleteLabel = (labelId: string) => {
    deleteLabel({ variables: { deleteLabelId: labelId } });
  };

  // Handle create new label
  const handleNewLabel = () => {
    if (newLabelName.trim()) {
      createLabel({
        variables: {
          name: newLabelName,
          isDefault: false,
        },
      });
      setNewLabelName("");
      setAddNewLabel(false);
    }
  };

  // Handle edit label
  const handleEditLabel = (labelId: string, labelName: string) => {
    setEditingLabelId(labelId);
    setEditingLabelName(labelName);
  };

  // Handle save edited label
  const handleSaveEditedLabel = () => {
    if (editingLabelId && editingLabelName.trim()) {
      updateLabel({
        variables: {
          updateLabelId: editingLabelId,
          input: {
            name: editingLabelName,
          },
        },
      });
      setEditingLabelId(null);
      setEditingLabelName("");
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingLabelId(null);
    setEditingLabelName("");
  };

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const { observerRef, hasMore } = useInfiniteQueryScroll({
    items: data?.getLabelsByFilter?.data || [],
    totalCount: data?.getLabelsByFilter?.totalCount || 0,
    loading,
    fetchMore,
    scrollContainerRef,
  });

  return (
    <div className="flex flex-col gap-y-2">
      <p>
        Select a label to apply to the selected customers. You can also create a
        new label.
      </p>
      <Divider />
      <RadioGroup
        value={selectedLabelId || ""}
        onValueChange={setSelectedLabelId}
        classNames={{ wrapper: "flex flex-col gap-y-2" }}
        className="max-h-60 overflow-y-scroll scrollbar"
        ref={scrollContainerRef}
      >
        {data?.getLabelsByFilter?.data?.map(
          (label: LabelType, index: number) => (
            <div
              key={label._id}
              className="group flex w-full justify-between"
              ref={
                index === data.getLabelsByFilter.data.length - 1 && hasMore
                  ? observerRef
                  : null
              }
            >
              {editingLabelId === label._id ? (
                <div className="flex items-center gap-x-2 w-full">
                  <Input
                    classNames={{
                      inputWrapper: "rounded-md",
                      label: "text-sm",
                    }}
                    value={editingLabelName}
                    onValueChange={setEditingLabelName}
                    placeholder="Enter label name"
                    size="sm"
                    className="flex-grow"
                  />
                  <div className="flex gap-x-2">
                    <Button
                      isIconOnly
                      startContent={<BiX className="text-2xl" />}
                      onPress={handleCancelEdit}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                    <Button
                      isIconOnly
                      startContent={<BiCheck className="text-2xl" />}
                      onPress={handleSaveEditedLabel}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                  </div>
                </div>
              ) : (
                <>
                  <Radio value={label._id}>
                    <div className="text-small">
                      {label.name}{" "}
                      <small>
                        ({label.customers?.totalCount || 0} Customers)
                      </small>
                    </div>
                  </Radio>
                  <div className="group-hover:flex opacity-0 group-hover:opacity-100 gap-x-1">
                    <Button
                      isIconOnly
                      startContent={<BiPencil />}
                      onPress={() => handleEditLabel(label._id, label.name)}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                    <Button
                      isIconOnly
                      startContent={<BiTrash />}
                      onPress={() => handleDeleteLabel(label._id)}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                  </div>
                </>
              )}
            </div>
          )
        )}
        {loading && <Spinner className="mt-4" />}
      </RadioGroup>

      {addNewLabel ? (
        <div className="flex items-center gap-x-2">
          <Input
            classNames={{ inputWrapper: "rounded-md " }}
            value={newLabelName}
            onValueChange={setNewLabelName}
            placeholder="Enter new label name"
            size="sm"
          />
          <div className="flex gap-x-2">
            <Button
              isIconOnly
              startContent={<BiX className="text-2xl" />}
              onPress={() => {
                setAddNewLabel(false);
                setNewLabelName("");
              }}
              className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
              size="sm"
              variant="ghost"
              radius="full"
            />
            <Button
              isIconOnly
              startContent={<BiCheck className="text-2xl" />}
              onPress={handleNewLabel}
              className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
              size="sm"
              variant="ghost"
              radius="full"
            />
          </div>
        </div>
      ) : (
        <button
          className="text-primary text-small flex items-center gap-x-2"
          onClick={() => setAddNewLabel(true)}
        >
          <BiPlus className="text-lg" />
          <span>Add New Label</span>
        </button>
      )}
    </div>
  );
};

export default EditLabelsModal;
