import { <PERSON><PERSON>, BreadcrumbI<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Form } from "@heroui/react";
import CardWrapper from "../components/CardWrapper";
import { SlLocationPin } from "react-icons/sl";
import AddItemButton from "../components/orders/AddItemButton";
import { CustomersType, CustomStateSelect } from "../components/forms/CustomSelect";
import ModalComponent from "../components/ModalComponent";
import SelectProduct from "../components/orders/SelectProduct";
import { useQuery } from "@apollo/client";
import CartItem from "../components/orders/CartItem";
import SelectedCustomer from "../components/orders/SelectedCustomer";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { OrderSchema, PaymentStatus } from "../types/orderType";
import { orderValidationSchema } from "../validation/orderValidationSchema";
import { formatDateNative, formatTimeTo12Hour } from "../helpers/formatters";
import DeliveryMethodModal from "../components/orders/DeliveryMethodModal";
import AddFeeModal from "../components/orders/AddFeeModal";
import AddDiscountModal from "../components/orders/AddDiscountModal";
import LocationSelectionModal from "../components/orders/LocationSelectionModal";
import { useAddEditOrderModal } from "../hooks/useAddEditOrderModal";
import { GET_CUSTOMERS } from "../graphql/customers";
import { AddressType, CustomerType } from "../types/customerType";
import { useBoundStore } from "../store/store";

const AddEditOrders = () => {
	const { productSelectModal, deliveryMethodModal, feeModal, discountModal, locationModal } =
		useAddEditOrderModal();
	const { selectedProductId, setSelectedProductId, cartItem, setCartItem } = useBoundStore();
	console.log(selectedProductId, " seelctedproductid");
	const { data: customerData } = useQuery(GET_CUSTOMERS, {
		variables: { limit: 100, offset: 0, filters: {} },
		notifyOnNetworkStatusChange: true,
	});

	const users = customerData?.getUsersByFilters?.data?.map((user: CustomerType) => ({
		label: user.firstName + " " + user.lastName,
		value: user._id,
		email: user.email,
		_id: user._id,
		profileImg: user.profileImg,
	}));

	const {
		control,
		formState: { errors, isSubmitting },
		getValues,
		handleSubmit,
		setValue,
		trigger,
		watch,
	} = useForm<OrderSchema>({
		resolver: zodResolver(orderValidationSchema),
		defaultValues: {
			cart: [],
			customFee: [],
			customDiscount: [],
			userData: {
				firstName: "",
				lastName: "",
				email: "",
				phone: "",
				countryCode: "",
			},
			customeDeliveryOptions: {
				schedule: false,
				pickup: false,
				date: "",
			},
			totalPrice: 0,
			totalDiscount: 0,
			taxAmount: 0,
			tagIds: [],
			shippingAmount: 0,
			shippingAddressId: "",
			shippingAddress: {
				addressType: AddressType.HOME,
				flat: 0,
				addressline1: "",
				addressline2: "",
				landmark: "",
				countryCode: "",
				phone: "",
				city: "",
				country: "",
				states: "",
				pincode: "",
				primary: true,
			},
			seenByHuman: false,
			orderPrice: 0,
			itemcount: 0,
			ipAddress: "",
			paymentStatus: PaymentStatus.UNPAID,
			currency: "INR",
			location: {
				useLocation: false,
				address: "",
			},
		},
	});
	const handleOrderSaveClick = async () => {
		// Logic to save the order
		await handleSubmit(onSubmit)();
	};

	const handleSelectLocation = () => {
		locationModal.onOpen();
	};

	const handleAddProductFromList = () => {
		const existingCartItem = getValues("cart");
		if (cartItem) {
			const isProductAlreadyAdded = existingCartItem.some(
				(item) => item.productId === cartItem._id
			);
			if (!isProductAlreadyAdded) {
				setValue("cart", [
					...existingCartItem,
					{
						finalPrice: cartItem.price || 0,
						name: cartItem.name,
						price: cartItem.price || 0,
						productId: cartItem?._id,
						qty: 1,
						asset: cartItem.assets?.[0]
							? {
									path: cartItem.assets[0].path,
									type: "IMAGE" as const,
									altText: cartItem.name,
									isFeatured: false,
							  }
							: undefined,
						categoryId: cartItem.categories?.[0]?._id,
					},
				]);
			}
			setCartItem(null);
		}
		setSelectedProductId(null);

		productSelectModal.onClose();
	};

	const handleAddProduct = () => {
		productSelectModal.onOpen();
	};
	const handleAddService = () => {
		console.log("add service");
	};
	const handleAddCustomItem = () => {};

	const renderCustomerSelectOption = (data: CustomersType) => {
		return (
			<div key={data._id} className="flex gap-2 items-center">
				<Avatar alt={data.label} className="flex-shrink-0" size="sm" src={data.profileImg} />
				<div className="flex flex-col">
					<span>{data.label}</span>
					<span className="text-default-500 text-tiny">({data.email})</span>
				</div>
			</div>
		);
	};

	const handleDeliveryMethodAdd = async () => {
		const isValid = await trigger("customeDeliveryOptions.name");
		if (!isValid) {
			return; // Prevent closing the modal if validation fails
		}
		deliveryMethodModal.onClose();
	};

	const handleAddFee = async () => {
		// Get current fee options
		const feeOptions = watch("customFee");
		let isValid = true;

		// Check if at least one fee option has both name and rate
		for (let i = 0; i < feeOptions.length; i++) {
			if (feeOptions[i].name && !feeOptions[i].amount) {
				// If there's a name but no rate, validate the rate field
				await trigger(`customFee.${i}.amount`);
				isValid = false;
			} else if (!feeOptions[i].name && feeOptions[i].amount > 0) {
				// If there's a rate but no name, validate the name field
				await trigger(`customFee.${i}.name`);
				isValid = false;
			}
		}

		if (isValid) {
			// Filter out empty fee options (no name or zero rate)
			const validFeeOptions = feeOptions.filter((fee) => fee.name && fee.amount > 0);

			// If there are no valid fee options, add an empty one as placeholder
			if (validFeeOptions.length === 0) {
				setValue("customFee", [{ name: "", amount: 0 }]);
			} else {
				// Otherwise, set only the valid fee options
				setValue("customFee", validFeeOptions);
			}

			feeModal.onClose();
		}
	};

	const handleAddDiscount = async () => {
		// Get current discount options
		const discountOptions = watch("customDiscount");
		let isValid = true;

		// Check if at least one discount option has both name and rate
		for (let i = 0; i < discountOptions.length; i++) {
			if (discountOptions[i].discountReason && !discountOptions[i].amount) {
				// If there's a name but no rate, validate the rate field
				await trigger(`customDiscount.${i}.amount`);
				isValid = false;
			} else if (!discountOptions[i].discountReason && discountOptions[i].amount > 0) {
				// If there's a rate but no name, validate the name field
				await trigger(`customDiscount.${i}.discountReason`);
				isValid = false;
			}
		}

		if (isValid) {
			// Filter out empty discount options (no name or zero rate)
			const validDiscountOptions = discountOptions.filter(
				(discount) => discount.discountReason && discount.amount > 0
			);

			// If there are no valid discount options, add an empty one as placeholder
			if (validDiscountOptions.length === 0) {
				setValue("customDiscount", [{ discountReason: "", amount: 0 }]);
			} else {
				// Otherwise, set only the valid discount options
				setValue("customDiscount", validDiscountOptions);
			}

			discountModal.onClose();
		}
	};

	const handleLocationSave = () => {
		const locationData = watch("location");
		if (locationData.useLocation && !locationData.address) {
			// If location is enabled but address is empty, show validation error
			trigger("location.address");
			return;
		}
		locationModal.onClose();
	};
	console.log(errors, " errors");
	const onSubmit: SubmitHandler<OrderSchema> = (data) => {
		console.log(data, " seelted data");
	};
	return (
		<div>
			<ModalComponent
				size="2xl"
				modalHeader={"Add a delivery method"}
				isOpen={deliveryMethodModal.isOpen}
				onOpenChange={deliveryMethodModal.onOpenChange}
				onPress={handleDeliveryMethodAdd}
			>
				<DeliveryMethodModal errors={errors} control={control} watch={watch} setValue={setValue} />
			</ModalComponent>
			<ModalComponent
				isDismissable={false}
				isOpen={feeModal.isOpen}
				onOpenChange={feeModal.onOpenChange}
				modalHeader={"Add a fee"}
				size="3xl"
				onPress={handleAddFee}
				subheading={"Choose which fees to apply or add another one."}
			>
				<AddFeeModal errors={errors} control={control} setValue={setValue} watch={watch} />
			</ModalComponent>

			<ModalComponent
				isDismissable={false}
				isOpen={discountModal.isOpen}
				onOpenChange={discountModal.onOpenChange}
				modalHeader={"Add a discount"}
				size="3xl"
				onPress={handleAddDiscount}
				subheading={"Choose which discounts to apply or add another one."}
			>
				<AddDiscountModal errors={errors} control={control} setValue={setValue} watch={watch} />
			</ModalComponent>

			<ModalComponent
				size="3xl"
				onModalClose={() => {
					setSelectedProductId(null);
					productSelectModal.onClose();
				}}
				disabled={!selectedProductId}
				modalHeader={"Add a Product"}
				isOpen={productSelectModal.isOpen}
				onOpenChange={productSelectModal.onOpenChange}
				onPress={handleAddProductFromList}
			>
				<SelectProduct
					productId={selectedProductId}
					setProductId={(value: string) => {
						console.log(value, " value");
						setSelectedProductId(value);
					}}
				/>
			</ModalComponent>

			<ModalComponent
				size="md"
				modalHeader={"Add a business location (optional)"}
				isOpen={locationModal.isOpen}
				onPress={handleLocationSave}
				onOpenChange={locationModal.onOpenChange}
			>
				<LocationSelectionModal watch={watch} setValue={setValue} errors={errors} />
			</ModalComponent>
			<div className="grid grid-cols-1 py-2 md:grid-cols-2 justify-end items-center border-b mb-5 ml-1 sticky top-[3.15rem] bg-white dark:bg-slate-900 dark:border-slate-700 left-0 z-30 ">
				<div className="px-5">
					<Breadcrumbs>
						<BreadcrumbItem>Home</BreadcrumbItem>
						<BreadcrumbItem>Orders</BreadcrumbItem>
						<BreadcrumbItem>Add Order</BreadcrumbItem>
					</Breadcrumbs>
				</div>
				<div className="flex gap-x-4 w-full justify-end">
					<Button size="sm" radius="full" color="primary" variant="ghost">
						Cancel
					</Button>
					<Button
						disabled={isSubmitting}
						size="sm"
						radius="full"
						color="primary"
						isLoading={false}
						onPress={handleOrderSaveClick}
					>
						Save
					</Button>
				</div>
			</div>
			<div className="pl-2 md:pl-0">
				<h1>New Order</h1>
				<div className="flex gap-x-4 items-center">
					<div>Business Location: </div>
					{watch("location.useLocation") ? (
						<div className="flex items-center gap-2">
							<SlLocationPin className="text-primary" />
							<span>{watch("location.address")}</span>
							<Button size="sm" color="primary" variant="light" onPress={handleSelectLocation}>
								Change
							</Button>
						</div>
					) : (
						<Button
							size="sm"
							startContent={<SlLocationPin className="text-base" />}
							color="primary"
							variant="light"
							onPress={handleSelectLocation}
						>
							<span className="text-base">Select Location</span>
						</Button>
					)}
				</div>
			</div>
			<Form onSubmit={handleSubmit(onSubmit)} className="flex flex-col md:flex-row gap-y-4 mt-5">
				<div className="grid grid-cols-1 md:grid-cols-12 gap-4 mt-5 w-full">
					<div className="flex flex-col gap-y-4 col-span-7">
						<CardWrapper
							title="Items"
							endContent={
								<AddItemButton
									handleAddCustomItem={handleAddCustomItem}
									handleAddProduct={handleAddProduct}
									handleAddService={handleAddService}
								/>
							}
						>
							<div className="flex flex-col gap-y-1">
								{watch("cart").length > 0 ? (
									<div className="flex flex-col w-full space-y-4">
										{watch("cart").map((item, index) => (
											<CartItem
												key={index}
												item={item}
												index={index}
												setValue={setValue}
												watch={watch}
											/>
										))}
									</div>
								) : (
									<div className="flex flex-col justify-center w-full items-center">
										<h4>Add items to the order</h4>
										<p className="text-center">
											You can create an order with multiple items or add custom items (e.g., A gold
											ring with custom engraving).
										</p>
									</div>
								)}
								{errors.cart && !watch("cart") ? (
									<p className="text-red-500 text-sm mt-3 text-center">{errors.cart.message}</p>
								) : null}
							</div>
						</CardWrapper>
						<CardWrapper title={"Customer Info"}>
							{watch("userData.userId") ? (
								<SelectedCustomer
									users={customerData?.getUsersByFilters?.data || []}
									customerId={watch("userData.userId") ?? ""}
									setValue={setValue}
								/>
							) : (
								<Controller
									name="userData.userId"
									control={control}
									rules={{ required: "Customer is required" }}
									render={({ field }) => (
										<CustomStateSelect
											selectedKeys={field.value ? new Set([field.value]) : new Set()}
											onSelectionChange={({ currentKey }) => field.onChange(currentKey)}
											ariaLabel="Select Customer"
											name="userData.userId"
											renderValue={(data) => renderCustomerSelectOption(data)}
											selectItem={(data) => renderCustomerSelectOption(data)}
											placeholder="Select from your customers list or add a new one"
											isInvalid={!!errors.userData?.userId}
											users={users}
										/>
									)}
								/>
							)}
							{errors.userData?.userId ? (
								<p className="text-red-500 text-sm ">{errors.userData?.userId?.message}</p>
							) : null}
						</CardWrapper>
					</div>
					<div className="col-span-7 md:col-span-5">
						<CardWrapper title="Order Summary">
							<div className="text-sm grid grid-cols-2 w-full border-b pb-3">
								<div>
									<p>Items</p>
									{watch("deliveryMethod") === "CUSTOM" ? null : (
										<button
											onClick={() => deliveryMethodModal.onOpen()}
											type="button"
											className="underline text-primary"
										>
											Add Delivery Method
										</button>
									)}
								</div>
								<p className="w-full text-right">₹ {watch("totalPrice").toFixed(2)}</p>
							</div>
							{watch("deliveryMethod") === "CUSTOM" ? (
								<div className="text-sm grid grid-cols-2 w-full border-b mt-2 pb-2">
									<div>
										<p>{watch("customeDeliveryOptions.name")}</p>
										<p className="text-gray-600 text-xs flex text-nowrap">
											{watch("customeDeliveryOptions.date")
												? formatDateNative(watch("customeDeliveryOptions.date") || "")
												: null}{" "}
											{watch("customeDeliveryOptions.fromTime") &&
												formatTimeTo12Hour(watch("customeDeliveryOptions.fromTime") || "")}{" "}
											{watch("customeDeliveryOptions.totime") &&
												formatTimeTo12Hour(watch("customeDeliveryOptions.totime") || "")}{" "}
										</p>
										<button
											onClick={() => deliveryMethodModal.onOpen()}
											type="button"
											className="underline text-primary focus:outline-none focus:border-none"
										>
											Edit Delivery Method
										</button>
									</div>
									<p className="w-full text-right">
										{watch("customeDeliveryOptions.rate")
											? `₹ ${watch("customeDeliveryOptions.rate")?.toFixed(2)}`
											: null}
									</p>
								</div>
							) : null}
							{/* Fee Options */}
							{(() => {
								// Filter out empty fees (those with no name or zero rate)
								const validFees = watch("customFee").filter((fee) => fee.name && fee.amount > 0);
								if (validFees.length > 0) {
									// Show fees with values
									return (
										<div className="text-sm w-full border-b mt-1 pb-3">
											{/* List all valid fee options */}
											<div className=" space-y-1 mt-2">
												{validFees.map((fee, index) => (
													<div key={index} className="grid grid-cols-2">
														<p className="text-gray-600">{fee.name}</p>
														<p className="w-full text-right text-gray-600">
															₹ {fee.amount.toFixed(2)}
														</p>
													</div>
												))}
											</div>
											<div className="flex justify-between mb-2">
												<button
													type="button"
													className="underline text-primary text-nowrap focus:border-none focus:outline-none"
													onClick={() => feeModal.onOpen()}
												>
													Edit Fees
												</button>
											</div>
										</div>
									);
								} else {
									// Show only Add Fees button when no valid fees
									return (
										<div className="text-sm grid grid-cols-2 w-full border-b mt-1 pb-3">
											<div>
												<button
													type="button"
													className="underline text-primary mt-1"
													onClick={() => {
														// Initialize with empty fee if none exist
														if (watch("customFee").length === 0) {
															setValue("customFee", [{ name: "", amount: 0 }]);
														}
														feeModal.onOpen();
													}}
												>
													Add Fee
												</button>
											</div>
											<p className="w-full text-right">₹ 0.00</p>
										</div>
									);
								}
							})()}
							{/* Discount Options */}
							{(() => {
								// Filter out empty discounts (those with no name or zero rate)
								const validDiscounts = watch("customDiscount").filter(
									(discount) => discount.discountReason && discount.amount > 0
								);

								if (validDiscounts.length > 0) {
									// Show discounts with values
									return (
										<div className="text-sm w-full border-b mt-1 pb-3">
											{/* List all valid discount options */}
											<div className=" space-y-1 mt-2">
												{validDiscounts.map((discount, index) => (
													<div key={index} className="grid grid-cols-2">
														<p className="text-gray-600">{discount.discountReason}</p>
														<p className="w-full text-right">-₹ {discount.amount.toFixed(2)}</p>
													</div>
												))}
											</div>
											<div className="flex justify-between mb-2">
												<button
													type="button"
													className="underline text-primary text-nowrap focus:border-none focus:outline-none"
													onClick={() => discountModal.onOpen()}
												>
													Edit Discounts
												</button>
											</div>
										</div>
									);
								} else {
									// Show only Add Discounts button when no valid discounts
									return (
										<div className="text-sm grid grid-cols-2 w-full border-b mt-1 pb-3">
											<div>
												<button
													type="button"
													className="underline text-primary mt-1"
													onClick={() => {
														// Initialize with empty discount if none exist
														if (watch("customDiscount").length === 0) {
															setValue("customDiscount", [{ discountReason: "", amount: 0 }]);
														}
														discountModal.onOpen();
													}}
												>
													Add Discount
												</button>
											</div>
											<p className="w-full text-right">₹ 0.00</p>
										</div>
									);
								}
							})()}

							<div className="text-sm grid grid-cols-2 w-full border-b mt-2 pb-3">
								<p>Tax</p>
								<p className="w-full text-right">₹ 0.00</p>
							</div>
							<div className="text-sm grid grid-cols-2 w-full border-b mt-2 pb-3">
								<p className="font-bold">Total</p>
								<p className="w-full text-right font-bold">
									₹{" "}
									{(() => {
										// Filter out empty fees (those with no name or zero rate)
										const validFees = watch("customFee").filter(
											(fee) => fee.name && fee.amount > 0
										);

										// Filter out empty discounts (those with no name or zero rate)
										const validDiscounts = watch("customDiscount").filter(
											(discount) => discount.discountReason && discount.amount > 0
										);

										// Calculate total fees
										const totalFees = validFees.reduce((sum, fee) => sum + fee.amount, 0);

										// Calculate total discounts
										const totalDiscounts = validDiscounts.reduce(
											(sum, discount) => sum + discount.amount,
											0
										);

										// Calculate final total
										const total =
											watch("cart")?.reduce((sum, item) => sum + item.finalPrice, 0) +
											(watch("customeDeliveryOptions")?.rate || 0) +
											totalFees -
											totalDiscounts;

										return total.toFixed(2);
									})()}
								</p>
							</div>
						</CardWrapper>
					</div>
				</div>
				{/* <Button type="submit">Submit</Button> */}
			</Form>
		</div>
	);
};

export default AddEditOrders;
