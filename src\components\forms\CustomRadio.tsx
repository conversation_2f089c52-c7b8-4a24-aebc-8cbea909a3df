import { cn, useRadio, VisuallyHidden } from "@heroui/react";

export const CustomRadio = ({
  endContent,
  ...props
}: { endContent?: React.ReactNode } & any) => {
  const {
    Component,
    children,
    description,
    getBaseProps,
    getWrapperProps,
    getInputProps,
    getLabelProps,
    getLabelWrapperProps,
    getControlProps,
  } = useRadio(props);

  return (
    <Component
      {...getBaseProps()}
      className={cn(
        "group inline-flex items-center hover:opacity-70 active:opacity-50 justify-between tap-highlight-transparent",
        "max-w-full w-full cursor-pointer border border-lightPrimary rounded-lg gap-4 px-4 py-2",
        "data-[selected=true]:border-primary"
      )}
    >
      <div className="flex items-center w-full">
        <VisuallyHidden>
          <input {...getInputProps()} />
        </VisuallyHidden>
        <span {...getWrapperProps()}>
          <span {...getControlProps()} />
        </span>
        <div
          {...getLabelWrapperProps()}
          className="text-tiny flex flex-col pl-2"
        >
          {children && (
            <span {...getLabelProps({ style: { fontSize: "14px" } })}>
              {children}
            </span>
          )}
          {description && (
            <span className="text-tiny text-foreground opacity-70">
              {description}
            </span>
          )}
        </div>
      </div>
      {endContent && <div>{endContent}</div>}
    </Component>
  );
};
