import { Button, Checkbox, Input } from "@heroui/react";
import { Controller } from "react-hook-form";
import { BiTrash } from "react-icons/bi";

type CustomTextProps = {
  index: number;
  control: any;
  errors: any;
  setFormValue: any;
  removeCustomTextField: (index: number) => void;
  customTextRemove: (index: number) => void;
};
const CustomTexts = ({
  index,
  control,
  errors,
  setFormValue,
  removeCustomTextField,
  customTextRemove,
}: CustomTextProps) => {
  return (
    <div className="flex w-full  gap-4 mb-4">
      <div className="flex flex-col w-full gap-2">
        <div className="flex gap-4 w-full">
          <Controller
            name={`customTexts.${index}.title`}
            control={control}
            render={({ field }) => (
              <Input
                label="Title"
                {...field}
                labelPlacement="outside"
                errorMessage={errors.customTexts?.[index]?.title?.message}
                isInvalid={!!errors.customTexts?.[index]?.title}
                classNames={{
                  inputWrapper:
                    "w-full after:h-[1px] after:bg-primary rounded-md",
                }}
              />
            )}
          />
          <Controller
            name={`customTexts.${index}.charLimit`}
            control={control}
            render={({ field }) => (
              <Input
                label="Char Limit"
                {...field}
                labelPlacement="outside"
                onChange={(e) => field.onChange(Number(e.target.value))} // Convert to number
                errorMessage={errors.customTexts?.[index]?.charLimit?.message}
                value={field.value?.toString() || ""} // Ensure value is a string
                isInvalid={!!errors.customTexts?.[index]?.charLimit}
                classNames={{
                  inputWrapper:
                    "w-full after:h-[1px] after:bg-primary rounded-md",
                }}
              />
            )}
          />
        </div>
        <div>
          <Controller
            name={`customTexts.${index}.isRequired`}
            control={control}
            render={({ field: { onChange, ref, name, value } }) => (
              <Checkbox
                ref={ref}
                name={name}
                isSelected={value}
                onValueChange={(value) => {
                  onChange(value);
                  setFormValue(name, value);
                }}
                className="checkboxMenu"
              />
            )}
          />
          <label htmlFor="isRequired">Mandatory Field</label>
        </div>
      </div>

      <Button
        color="primary"
        onPress={() => {
          removeCustomTextField(index);
          customTextRemove(index);
        }}
        size="sm"
        variant="light"
        isIconOnly
        radius="full"
      >
        <BiTrash className="text-xl" />
      </Button>
    </div>
  );
};

export default CustomTexts;
