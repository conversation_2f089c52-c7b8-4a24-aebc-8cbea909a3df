import {
	Accordion,
	AccordionItem,
	Button,
	DateInput,
	Input,
	NumberInput,
	Radio,
	RadioGroup,
	Select,
	SelectItem,
	Spinner,
} from "@heroui/react";
import { FulfillmentStatus, OrderFilters, PaymentStatus } from "../../types/orderType";
import { parseDate, CalendarDate } from "@internationalized/date";
import { GET_ORDER_TAGS } from "../../graphql/orderTags";
import { useQuery } from "@apollo/client";

type OrderTableFilters = {
	filters: OrderFilters;
	setFilters: (value: Partial<OrderFilters>) => void;
};
const OrdersFilters = ({ filters, setFilters }: OrderTableFilters) => {
	const { data, loading, error } = useQuery(GET_ORDER_TAGS, {
		variables: { limit: 100, offset: 0, filters: { search: null } },
		notifyOnNetworkStatusChange: true,
	});

	if (error && !loading) {
		return (
			<div className="w-full h-full text-center pt-20">
				<p>Failed to load order tags</p>
			</div>
		);
	}

	if (loading) {
		return (
			<div className="w-full h-full text-center pt-20">
				<Spinner size="lg" />
			</div>
		);
	}

	return (
		<div className="p-4">
			<h3 className="text-lg font-semibold mb-4">Filter Orders</h3>
			<Accordion isCompact selectionMode="multiple">
				<AccordionItem key="1" aria-label="Accordion 1" title="Payment Status">
					<RadioGroup
						aria-label="Filter by Payment Status"
						size="sm"
						classNames={{ label: "text-sm" }}
						onValueChange={(value) => setFilters({ paymentStatus: value as PaymentStatus })}
						value={filters.paymentStatus}
					>
						{Object.values(PaymentStatus).map((status) => (
							<Radio key={status} value={status}>
								{status}
							</Radio>
						))}
					</RadioGroup>
				</AccordionItem>
				<AccordionItem key="2" aria-label="Filter by Fulfillment Status" title="Fulfillment Status">
					<RadioGroup
						aria-label="Filter by Fulfillment Status"
						size="sm"
						classNames={{ label: "text-sm" }}
						onValueChange={(value) => setFilters({ fulfillmentStatus: value as FulfillmentStatus })}
						value={filters.fulfillmentStatus}
					>
						{Object.values(FulfillmentStatus).map((status) => (
							<Radio key={status} value={status}>
								{status}
							</Radio>
						))}
					</RadioGroup>
				</AccordionItem>
				<AccordionItem key={"3"} aria-label="Filter by order no" title="Order No.">
					<Input
						size="sm"
						aria-label="Order No."
						value={filters.orderNo ?? ""}
						placeholder="Order No."
						onValueChange={(value) => setFilters({ orderNo: value })}
						classNames={{
							inputWrapper: "rounded-md",
							label: "text-sm",
						}}
					/>
				</AccordionItem>

				<AccordionItem key={"4"} aria-label="Filter by date" title="Start & End Date">
					<div className="flex w-full gap-3">
						<DateInput
							className="max-w-sm"
							size="sm"
							labelPlacement="outside"
							label={"Start date"}
							onChange={(value) => {
								if (value) {
									const date = new CalendarDate(value.year, value.month, value.day);
									setFilters({ startDate: date.toString() });
								}
							}}
							value={filters.startDate ? parseDate(filters.startDate) : null}
							placeholderValue={new CalendarDate(1995, 11, 6)}
						/>

						<DateInput
							className="max-w-sm"
							size="sm"
							labelPlacement="outside"
							label={"End date"}
							onChange={(value) => {
								if (value) {
									const date = new CalendarDate(value.year, value.month, value.day);
									setFilters({ endDate: date.toString() });
								}
							}}
							value={filters.endDate ? parseDate(filters.endDate) : null}
							placeholderValue={new CalendarDate(1995, 11, 6)}
						/>
					</div>
				</AccordionItem>
				<AccordionItem key={"5"} aria-label="Filter by tags" title="Tags">
					<Select
						className="max-w-xs w-full"
						classNames={{
							trigger: "rounded-md",
						}}
						size="sm"
						placeholder="Select tags"
						selectedKeys={filters.tagIds || undefined}
						selectionMode="multiple"
						onSelectionChange={(value) => {
							setFilters({ tagIds: Array.from(value as Set<string>) });
						}}
					>
						{data?.getOrderTagsByFilter?.data.map((tag: { _id: string; name: string }) => (
							<SelectItem key={tag._id}>{tag.name}</SelectItem>
						))}
					</Select>
				</AccordionItem>

				<AccordionItem key={"6"} aria-label="Filter by total price" title="Total Price">
					<div className="flex w-full gap-3">
						<NumberInput
							size="sm"
							hideStepper
							label="Min Total Price"
							labelPlacement="outside"
							aria-label="Min Total Price"
							value={filters.minTotalPrice ?? 0}
							placeholder="0"
							onValueChange={(value) => setFilters({ minTotalPrice: value })}
							classNames={{
								inputWrapper: "rounded-md",
								label: "text-sm",
							}}
						/>
						<NumberInput
							size="sm"
							hideStepper
							label="Max Total Price"
							labelPlacement="outside"
							aria-label="Max Total Price"
							value={filters.maxTotalPrice ?? 0}
							placeholder="0"
							onValueChange={(value) => setFilters({ maxTotalPrice: value })}
							classNames={{
								inputWrapper: "rounded-md",
								label: "text-sm",
							}}
						/>
					</div>
				</AccordionItem>
			</Accordion>

			<Button
				variant="ghost"
				size="sm"
				radius="full"
				color="primary"
				className="mt-10"
				onPress={() =>
					setFilters({
						paymentStatus: null,
						fulfillmentStatus: null,
						searchTerm: null,
						startDate: null,
						endDate: null,
						tagIds: null,
						isManual: null,
						isArchived: null,
						orderNo: null,
						userId: null,
						maxTotalPrice: null,
						minTotalPrice: null,
					})
				}
			>
				Clear All Filters{" "}
			</Button>
		</div>
	);
};

export default OrdersFilters;
