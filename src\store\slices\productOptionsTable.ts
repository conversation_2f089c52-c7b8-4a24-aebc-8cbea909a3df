import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import { ProductVariantsType } from "../../types/productType";

export interface ProductOptions {
  variants: ProductVariantsType[];
  addVariant: (variant: ProductVariantsType[]) => void;
  trackInventory: boolean;
  setVariantValue: <K extends keyof ProductVariantsType>(
    _id: string,
    key: K,
    value: ProductVariantsType[K]
  ) => void;
  setTrackInventory: () => void;
  resetVariants: () => void;
}

const initialState: Omit<
  ProductOptions,
  "addVariant" | "setVariantValue" | "setTrackInventory" | "resetVariants"
> = {
  variants: [],
  trackInventory: false,
};
export const createProductOptionsSlice: StateCreator<
  ProductOptions,
  [],
  [["zustand/immer", never]]
> = immer((set) => ({
  ...initialState,
  addVariant: (variant: ProductVariantsType[]) => {
    set((state) => {
      state.variants = variant;
    });
  },
  setVariantValue: <K extends keyof ProductVariantsType>(
    _id: string,
    key: K,
    value: ProductVariantsType[K]
  ) => {
    set((state) => ({
      variants: state.variants.map((variant) => {
        if (variant._id === _id) {
          // Check if the key is 'priceDifference'
          if (key === "priceDifference" && typeof value === "number") {
            // Calculate the new variantPrice
            const newVariantPrice = variant.variantPrice + value;
            // Return the updated variant with the new variantPrice
            return {
              ...variant,
              [key]: value,
              variantPrice: isNaN(newVariantPrice) ? 0.0 : newVariantPrice,
            };
          }
          // For other keys, just update the value
          return { ...variant, [key]: value };
        }
        return variant;
      }),
    }));
  },
  setTrackInventory: () => {
    set((state) => {
      state.trackInventory = !state.trackInventory;
    });
  },
  resetVariants: () => {
    set((state) => {
      state.variants = [];
      state.trackInventory = false;
    });
  },
}));
