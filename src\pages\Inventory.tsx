import { But<PERSON>, Image, Select, SelectItem, NumberInput } from "@heroui/react";

import { useCallback, useMemo } from "react";
import { BiExport } from "react-icons/bi";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import { useDisclosure } from "@heroui/react";
import { RxDividerVertical } from "react-icons/rx";
import TableComponent from "../components/Table";
import { CiExport } from "react-icons/ci";
import { ColumnType, GetProductsType, StatusType } from "../types/productType";
import TopHeader from "../components/table/TopHeader";
import TopDrawer from "../components/TopDrawer";

import { CSVLink } from "react-csv";
import { UPDATE_PRODUCT_VISIBILITY } from "../graphql/products";
import { useMutation, useQuery } from "@apollo/client";
import { useBoundStore } from "../store/store";
import { useMutationHandler } from "../hooks/useMutationStatusHandler";
import { StatusTypes } from "../types/commonTypes";
import {
	BULK_UPDATE_INVENTORY_PRODUCT,
	GET_INVENTORY_PRODUCTS,
	UPDATE_INVENTORY_PRODUCT,
} from "../graphql/inventory";
import TableFilters from "../components/table/TableFilters";
import ModalComponent from "../components/ModalComponent";
import BulkUpdateInventory from "../components/inventory/BulkUpdateInventory";
import { GetInventoryType } from "../types/inventoryType";

export const columns: ColumnType[] = [
	{ name: "ID", uid: "_id", sortable: false },
	{ name: "Customers", uid: "name", sortable: false },
	{ name: "SKU", uid: "price", sortable: false },
	{ name: "Inventory", uid: "stockStatus" },
	{ name: "Visibility", uid: "isDeleted", sortable: false },
];

export const statusOptions: StatusType[] = [
	{ name: "PUBLISHED", uid: StatusTypes.PUBLISHED },
	{ name: "SCHEDULED", uid: StatusTypes.SCHEDULED },
	{ name: "ARCHIVED", uid: StatusTypes.ARCHIVED },
	{ name: "DRAFT", uid: StatusTypes.DRAFT },
];

const Inventory = () => {
	const {
		selectedKeys,
		setSelectedKeys,
		visibleColumns,
		setVisibleColumns,
		inventoryFilterText,
		setInventoryFilterText,
		setInventory,
		inventory,
		inventoryFilters,
		setInventoryFilters,
		bulkInventoryUpdate,
		resetBulkInventoryUpdate,
	} = useBoundStore();

	const { isOpen, onOpenChange, onClose, onOpen } = useDisclosure();
	const {
		isOpen: isBulkEditOpen,
		onOpenChange: onBulkEditOpenChange,
		onClose: onBulkEditClose,
		onOpen: onBulkEditOpen,
	} = useDisclosure();

	// used to update the product visiblity in bulk update
	const [
		updateProductVisibility,
		{
			data: productVisibilityData,
			loading: productVisibilityLoading,
			error: productVisibilityError,
		},
	] = useMutation(UPDATE_PRODUCT_VISIBILITY, {
		refetchQueries: [GET_INVENTORY_PRODUCTS, "GetInventoryProducts"],
	});

	const [
		updateInventoryProduct,
		{
			data: updateInventoryProductData,
			loading: updateInventoryProductLoading,
			error: updateInventoryProductError,
		},
	] = useMutation(UPDATE_INVENTORY_PRODUCT, {
		refetchQueries: [GET_INVENTORY_PRODUCTS, "GetInventoryProducts"],
	});

	const [
		bulkUpdateInventoryProducts,
		{
			data: bulkUpdateInventoryProductsData,
			loading: bulkUpdateInventoryProductsLoading,
			error: bulkUpdateInventoryProductsError,
		},
	] = useMutation(BULK_UPDATE_INVENTORY_PRODUCT, {
		refetchQueries: [GET_INVENTORY_PRODUCTS, "GetInventoryProducts"],
	});

	const { data, loading, fetchMore, refetch } = useQuery(GET_INVENTORY_PRODUCTS, {
		variables: {
			limit: 10,
			offset: 0,
			filters: { search: inventoryFilterText },
		},
		// fetchPolicy: "cache-and-network", // use 'cache-and-network' or 'network-only'
		notifyOnNetworkStatusChange: true,
		onCompleted: (data) => {
			if (data && data.getInventoryProducts && data.getInventoryProducts.products) {
				setInventory(data.getInventoryProducts.products);
			}
		},
	});

	const totalCount = data?.getInventoryProducts?.totalCount || 0;
	const hasMore = inventory.length < totalCount;

	const handleLoadMore = () => {
		if (!hasMore || loading) return;
		fetchMore({
			variables: {
				offset: inventory.length || 0,
				limit: 10,
				filters: { search: inventoryFilterText },
			},
			updateQuery: (prev, { fetchMoreResult }) => {
				if (!fetchMoreResult) return prev;
				const mergedProducts = [
					...prev.getInventoryProducts.products,
					...fetchMoreResult.getInventoryProducts.products,
				];
				setInventory(mergedProducts);

				return {
					getInventoryProducts: {
						__typename: prev.getInventoryProducts.__typename,
						products: mergedProducts,
						totalCount: fetchMoreResult.getInventoryProducts.totalCount,
					},
				};
			},
		});
	};

	useMutationHandler({
		data: productVisibilityData,
		loading: productVisibilityLoading,
		error: productVisibilityError,
		successMessage: "Product visibility updated successfully",
	});

	useMutationHandler({
		data: updateInventoryProductData,
		loading: updateInventoryProductLoading,
		error: updateInventoryProductError,
		successMessage: "Product inventory updated successfully",
	});

	useMutationHandler({
		data: bulkUpdateInventoryProductsData,
		loading: bulkUpdateInventoryProductsLoading,
		error: bulkUpdateInventoryProductsError,
		successMessage: "Product inventory updated successfully",
	});
	// used for rendering the cell data in the table
	const renderCell = useCallback(
		(item: GetInventoryType, columnKey: string) => {
			const cellValue = item[columnKey as keyof GetInventoryType];
			switch (columnKey) {
				case "name":
					return (
						<div className="flex items-center gap-x-2">
							<Image src="/images/table-test.webp" height={50} width={50} radius="sm" />
							<div>
								<h5>{item.name}</h5>
								<p className="text-textPlaceHolder text-small">
									{item.variants?.length} Variations |{" "}
									{item.categories?.map((c) => c.name).join(", ")}
								</p>
							</div>
						</div>
					);
				case "sku":
					return item.price;

				case "stockStatus":
					return (
						<div>
							{item.trackInventory ? (
								<NumberInput
									hideStepper
									size="sm"
									classNames={{
										inputWrapper: "w-40 rounded-md py-0 min-h-8 h-8",
									}}
									aria-label="Quantity"
									value={item.stockQuantity ?? 0}
								/>
							) : (
								<Select
									placeholder="Select"
									classNames={{
										trigger: "w-40 rounded-md py-0 min-h-8 h-8",
									}}
									aria-label="Inventory"
									defaultSelectedKeys={[
										item.stockStatus === "IN_STOCK" ? "IN_STOCK" : "OUT_OF_STOCK",
									]}
									onSelectionChange={(value) => {
										updateInventoryProduct({
											variables: {
												input: {
													productId: item._id,
												},
												stockStatus: value.currentKey,
											},
										});
									}}
								>
									<SelectItem key={"IN_STOCK"}>In Stock</SelectItem>
									<SelectItem key={"OUT_OF_STOCK"}>Out of Stock</SelectItem>
								</Select>
							)}
						</div>
					);
				case "isDeleted":
					return (
						<Select
							placeholder="Select"
							label=""
							classNames={{
								trigger: "w-40 rounded-md py-0 min-h-8 h-8",
							}}
							aria-label="Visibility"
							defaultSelectedKeys={[item.isDeleted ? "VISIBLE" : "INVISIBLE"]}
							onSelectionChange={(value) => {
								updateProductVisibility({
									variables: {
										productId: item._id,
										showInOnlineStore: String(value) === "VISIBLE" ? true : false,
									},
								});
							}}
						>
							<SelectItem key={"VISIBLE"}>Visible</SelectItem>
							<SelectItem key={"INVISIBLE"}>Invisible</SelectItem>
						</Select>
					);
				default:
					if (typeof cellValue === "object" && Array.isArray(cellValue)) {
						return (
							<div>
								{cellValue.map((val, idx) => (
									<span key={idx}>{JSON.stringify(val)}</span>
								))}
							</div>
						);
					}
					return typeof cellValue === "object" ? JSON.stringify(cellValue) : cellValue;
			}
		},
		[inventory]
	);

	// handling the product export in csv format
	const handlePhysicalProdCSVExport = () => {
		const headers = [
			{ label: "ID", key: "_id" },
			{ label: "Name", key: "name" },
			{ label: "Images", key: "images" },
			{ label: "Price", key: "price" },
			{ label: "Discounted Price", key: "discountedPrice" },
			{ label: "OnSale", key: "isOnSale" },
			{ label: "Collections", key: "categories" },
			{ label: "Variants", key: "variants" },
			{ label: "Track Inventory", key: "trackInventory" },
			{ label: "Total Product Quantity", key: "totalProductQuantity" },
			{ label: "Stock Status", key: "stockStatus" },
		];

		const finalData = inventory
			.filter((item: GetProductsType) => selectedKeys !== "all" && selectedKeys.has(item._id))
			.map((product: GetProductsType) => ({
				_id: product._id,
				name: product.name,
				images: product.assets.map((asset) => asset.path),
				price: product.price,
				discountedPrice: product.discountedPrice,
				isOnSale: product.isOnSale,
				categories: product.categories ? product.categories.map((category) => category.name) : "",
				variants: product.variants ? product.variants.length : 0,
				trackInventory: product.trackInventory,
				totalProductQuantity: product.totalProductQuantity,
				stockStatus: product.stockStatus,
			}));

		return {
			data: finalData,
			headers,
		};
	};

	// handling of the export and import process
	const exportImportDropDownItems = [
		{
			key: "export",
			label: "Export",
			description: "Export your physical products to a CSV file",
			icon: <CiExport className="text-lg" />,
			onPress: handlePhysicalProdCSVExport,
		},
	];

	// top header content for the table
	const topHeader = useMemo(() => {
		return (
			<TopHeader
				columns={columns}
				exportImportDropDownItems={exportImportDropDownItems}
				filterValue={inventoryFilterText}
				onSearchChange={setInventoryFilterText}
				setVisibleColumns={(cols) => setVisibleColumns(cols)}
				visibleColumns={visibleColumns}
				showItems={{
					columnsToggle: true,
					exportImportButton: false,
					filters: true,
					searchBar: true,
				}}
				onOpen={onOpen}
			/>
		);
	}, [visibleColumns, inventoryFilterText]);

	// top header when any of the row is selected
	const topSelectedContent = useMemo(() => {
		return (
			<div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 p-2 rounded-md">
				<p>
					{selectedKeys === "all" ? data?.getInventoryProducts?.products.length : selectedKeys.size}{" "}
					of {data?.getInventoryProducts?.products.length} Selected
				</p>
				<RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
				<div className="flex gap-x-3">
					<Button
						radius="full"
						color="primary"
						size="sm"
						onPress={handlePhysicalProdCSVExport}
						variant="ghost"
						className="border"
						startContent={<BiExport />}
					>
						<CSVLink
							asyncOnClick={true}
							filename="inventory-products-export.csv"
							headers={handlePhysicalProdCSVExport().headers}
							data={handlePhysicalProdCSVExport().data}
						>
							Export
						</CSVLink>
					</Button>
					<Button
						variant="ghost"
						color="primary"
						radius="full"
						size="sm"
						className="border"
						onPress={() => onBulkEditOpen()}
					>
						Update Inventory
					</Button>
				</div>
			</div>
		);
	}, [selectedKeys, inventory]);

	const handleAction = () => {
		refetch({
			filters: {
				status: inventoryFilters.status,
				stockStatus: inventoryFilters.stockStatus,
				productType: inventoryFilters.productType,
				search: inventoryFilterText,
			},
		}).then(() => onClose());
	};

	const finalSelectedKeys = useMemo(() => {
		if (selectedKeys === "all") {
			return inventory.map((item: GetProductsType) => item._id);
		}
		return Array.from(selectedKeys.values());
	}, [selectedKeys, inventory.length]);

	const handleBulkUpdate = () => {
		const finalProductIds = finalSelectedKeys.map((id) => ({ productId: id }));
		const bulInventoryPaylod = () => {
			if (bulkInventoryUpdate.trackInventory) {
				return {
					trackInventory: bulkInventoryUpdate.trackInventory,
					setQuantity: {
						setType: bulkInventoryUpdate.setQuantity.setType,
						value: bulkInventoryUpdate.setQuantity.value,
					},
				};
			} else {
				return {
					trackInventory: bulkInventoryUpdate.trackInventory,
					showAllItemAs: bulkInventoryUpdate.showAllItemAs,
				};
			}
		};
		const finalpayload = {
			input: finalProductIds,
			updateData: bulInventoryPaylod(),
		};
		console.log(finalpayload, " final payload");
		bulkUpdateInventoryProducts({
			variables: finalpayload,
		}).then((res) => {
			if (res.data) {
				resetBulkInventoryUpdate();
				onBulkEditClose();
			}
		});
		console.log("handle bulk update action here");
	};

	return (
		<>
			<div>
				<Breadcrumbs>
					<BreadcrumbItem>Home</BreadcrumbItem>
					<BreadcrumbItem>Inventory</BreadcrumbItem>
				</Breadcrumbs>
			</div>

			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<h1>Inventory {totalCount ? `(${totalCount})` : null}</h1>
				<div>&nbsp;</div>
			</div>
			<ModalComponent
				isOpen={isBulkEditOpen}
				onOpenChange={onBulkEditOpenChange}
				modalHeader={`Update inventory for ${finalSelectedKeys.length} product${
					finalSelectedKeys.length > 1 ? "s" : ""
				}`}
				size="3xl"
				onModalClose={() => {
					resetBulkInventoryUpdate();
					onBulkEditClose();
				}}
				disabled={(() => {
					if (bulkInventoryUpdate.trackInventory) {
						if (bulkInventoryUpdate.setQuantity.value === null) {
							return true;
						}
						return false;
					}
					if (!bulkInventoryUpdate.trackInventory) {
						if (bulkInventoryUpdate.showAllItemAs === null) {
							return true;
						}
						return false;
					}
				})()}
				onPress={handleBulkUpdate}
			>
				<BulkUpdateInventory finalSelectedKeys={finalSelectedKeys} />
			</ModalComponent>
			<TopDrawer
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				drawerHeader="Filters"
				handleAction={handleAction}
				isLoading={loading}
			>
				<TableFilters filters={inventoryFilters} setFilters={setInventoryFilters} />
			</TopDrawer>
			<TableComponent<GetInventoryType>
				columns={columns}
				isLoading={loading}
				hasMore={hasMore}
				list={{ items: inventory }}
				renderCell={renderCell}
				visibleColumns={visibleColumns}
				topContent={topHeader}
				topSelectedContent={topSelectedContent}
				selectedKeys={selectedKeys}
				setSelectedKeys={(keys) => setSelectedKeys(keys)}
				handleLoadMore={handleLoadMore}
			/>
		</>
	);
};

export default Inventory;
