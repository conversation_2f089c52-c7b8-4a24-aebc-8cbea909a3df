export enum CustomerMemberStatus {
	SITE_MEMBER = "SITE_MEMBER",
	SUBSCRIBED = "SUBSCRIBED",
	NEVER_SUBSCRIBED = "NEVER_SUBSCRIBED",
}

export enum CustomerUserGender {
	MALE = "MALE",
	FEMALE = "FEMALE",
	OTHER = "OTHER",
}

export enum AddressType {
	WORK = "WORK",
	HOME = "HOME",
	BILLING = "BILLING",
	SHIPPING = "SHIPPING",
	OTHER = "OTHER",
}
export interface CustomerAddressType {
	_id?: string;
	userId?: string;
	addressType: AddressType;
	flat: number;
	addressline1: string;
	addressline2: string;
	landmark: string;
	countryCode: string;
	phone: string;
	city: string;
	country: string;
	states: string;
	pincode: string;
	primary: boolean;
	createdAt: string;
	updatedAt: string;
}

export interface CustomerType {
	_id: string;
	firstName: string;
	lastName: string;
	email: string;
	gender: CustomerUserGender;
	dateOfBirth?: string;
	phoneNumber: string;
	emailSubscribedStatus: CustomerMemberStatus;
	lastActivity: string;
	labelIds: string[];
	address?: string;
	subscriptionStatus?: string;
	countryCode?: string;
	profileImg: string;
	addresses: CustomerAddressType[];
}

export interface GetCustomersResponse {
	customers: CustomerType[];
	totalCount: number;
}

export interface CustomerFilters {
	search: string;
	labelIds: string[];
}

export interface ColumnType {
	name: string;
	uid: string;
	sortable?: boolean;
}
