import React from "react";

interface CardWrapperProps {
  title?: string | null;
  endContent?: React.ReactNode;
  children: React.ReactNode;
}
const CardWrapper = ({
  title = null,
  endContent = null,
  children,
}: CardWrapperProps) => {
  const renderTitle = () => {
    if (title && endContent) {
      return (
        <div>
          <div className="flex w-full justify-between items-center  px-8">
            <h3 className="w-full py-4">{title}</h3>
            <div>{endContent}</div>
          </div>
          <div className="h-[1px] w-full bg-gray-200 dark:bg-slate-700"></div>
        </div>
      );
    }
    if (title) {
      return (
        <>
          <h3 className="w-full px-8 py-4">{title}</h3>
          <div className="h-[1px] w-full bg-gray-200 dark:bg-slate-700"></div>
        </>
      );
    }

    return null;
  };
  return (
    <div
      className={`bg-white border dark:border-slate-700 rounded-md  w-full dark:bg-slate-900 h-fit`}
    >
      {renderTitle()}
      <div className="p-5 md:p-8">{children}</div>
    </div>
  );
};

export default CardWrapper;
