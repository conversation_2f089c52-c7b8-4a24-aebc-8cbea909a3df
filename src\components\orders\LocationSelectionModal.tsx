import React from "react";
import { Input, Radio, RadioGroup } from "@heroui/react";
import { UseFormWatch, UseFormSetValue, UseFormTrigger } from "react-hook-form";
import { OrderSchema } from "../../types/orderType";

interface LocationSelectionModalProps {
  watch: UseFormWatch<OrderSchema>;
  setValue: UseFormSetValue<OrderSchema>;
  errors: any;
}

const LocationSelectionModal: React.FC<LocationSelectionModalProps> = ({
  watch,
  setValue,
  errors,
}) => {
  return (
    <div className="flex flex-col gap-4">
      <p className="text-sm text-gray-600">
        If you have multiple business locations, choose which location is
        associated with this order.
      </p>

      <RadioGroup
        value={watch("location.useLocation") ? "location" : "no-location"}
        onValueChange={(value) => {
          setValue("location.useLocation", value === "location");
          if (value === "no-location") {
            setValue("location.address", "");
          }
        }}
        orientation="vertical"
        className="gap-3"
      >
        <Radio value="no-location">No location</Radio>
        <Radio value="location">Select from your business locations</Radio>
      </RadioGroup>

      {watch("location.useLocation") && (
        <Input
          label="Location Address"
          placeholder="Enter location address"
          labelPlacement="outside"
          value={watch("location.address")}
          size="sm"
          onChange={(e) => setValue("location.address", e.target.value)}
          className="mt-2"
          classNames={{
            inputWrapper: "rounded-md",
          }}
          isInvalid={!!errors.location?.address}
          errorMessage={errors.location?.address?.message}
        />
      )}
    </div>
  );
};

export default LocationSelectionModal;
