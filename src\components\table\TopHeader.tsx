import { Button } from "@heroui/react";
import { BiFilter } from "react-icons/bi";
import { useEffect } from "react";
import useDebouncedInput from "../../hooks/useDebouncedInput";
import { ColumnType } from "../../types/productType";
import SearchBar from "./Searchbar";
import { ColumnToggleDropdown } from "./ColumnToggleDropdown";
import { ExportImportDropdown } from "./ExportImportDropdown";

export function capitalize(s: string): string {
	return s ? s.charAt(0).toUpperCase() + s.slice(1).toLowerCase() : "";
}
export interface ExportImportDropdownItem {
	key: string;
	label: string;
	description: string;
	icon: React.JSX.Element;
	onPress: () => void;
}

interface TopHeaderProps {
	filterValue?: string;
	onSearchChange?: (value: string) => void;
	onClear?: () => void;
	columns: ColumnType[];
	onOpen?: () => void;
	exportImportDropDownItems?: ExportImportDropdownItem[];
	visibleColumns: Set<string>;
	setVisibleColumns: (columns: Set<string>) => void;
	showItems?: {
		searchBar?: boolean;
		exportImportButton?: boolean;
		columnsToggle?: boolean;
		filters?: boolean;
	};
}

const TopHeader = ({
	filterValue = "",
	onSearchChange,
	onClear,
	columns,
	onOpen,
	exportImportDropDownItems = [],
	visibleColumns,
	setVisibleColumns,
	showItems = {
		searchBar: true,
		exportImportButton: true,
		columnsToggle: true,
		filters: true,
	},
}: TopHeaderProps) => {
	const { searchValue, handleSearchChange, setSearchValue } = useDebouncedInput({
		initialValue: filterValue,
		delay: 400,
		onChange: onSearchChange,
	});

	useEffect(() => {
		if (filterValue !== searchValue) setSearchValue(filterValue);
	}, [filterValue]);

	const handleClear = () => {
		setSearchValue("");
		onClear?.();
	};

	return (
		<div className="flex flex-col gap-4 bg-white dark:bg-slate-900 p-2 rounded-md">
			<div className="flex justify-between items-center gap-3">
				<div className="hidden md:block" />
				<div className="flex mx-auto md:mx-0 gap-3">
					{showItems.searchBar && (
						<SearchBar value={searchValue} onChange={handleSearchChange} onClear={handleClear} />
					)}
					{showItems.exportImportButton && (
						<ExportImportDropdown items={exportImportDropDownItems} />
					)}
					{showItems.columnsToggle && (
						<ColumnToggleDropdown
							columns={columns}
							visibleColumns={visibleColumns}
							setVisibleColumns={setVisibleColumns}
						/>
					)}
					{showItems.filters && (
						<Button
							color="primary"
							className="border"
							onPress={onOpen}
							size="sm"
							variant="ghost"
							radius="full"
							endContent={<BiFilter />}
						>
							Filters
						</Button>
					)}
				</div>
			</div>
		</div>
	);
};

export default TopHeader;
