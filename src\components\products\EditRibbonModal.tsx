import { useMutation, useQuery } from "@apollo/client";
import {
  <PERSON><PERSON>,
  Divider,
  Input,
  Radio,
  RadioGroup,
  Spinner,
} from "@heroui/react";
import { useRef, useState } from "react";
import {
  CREATE_RIBBON,
  DELETE_RIBBON,
  FETCH_RIBBONS,
  UPDATE_RIBBON,
} from "../../graphql/ribbons";
import { useBoundStore } from "../../store/store";
import { BiCheck, BiPencil, BiPlus, BiTrash, BiX } from "react-icons/bi";
import { useMutationHandler } from "../../hooks/useMutationStatusHandler";
import { useInfiniteQueryScroll } from "../../hooks/useInfiniteQueryScroll";

interface RibbonType {
  _id: string;
  name: string;
  products: {
    totalCount: number;
  };
}

const EditRibbonModal = () => {
  const [addNewRibbon, setAddNewRibbon] = useState(false);
  const [newRibbonName, setNewRibbonName] = useState("");
  const [editingRibbonId, setEditingRibbonId] = useState<string | null>(null);
  const [editingRibbonName, setEditingRibbonName] = useState("");
  const { selectedRibbonId, setSelectedRibbonId } = useBoundStore();
  const { data, loading, fetchMore } = useQuery(FETCH_RIBBONS, {
    variables: { limit: 10, offset: 0, filters: {} },
    notifyOnNetworkStatusChange: true,
  });

  const [
    deleteRibbon,
    {
      data: deleteRibbonData,
      loading: deleteRibbonLoading,
      error: deleteRibbonError,
    },
  ] = useMutation(DELETE_RIBBON, {
    refetchQueries: [FETCH_RIBBONS, "getRibbonsByFilter"],
  });

  const [
    createRibbon,
    {
      data: createRibbonData,
      loading: createRibbonLoading,
      error: createRibbonError,
    },
  ] = useMutation(CREATE_RIBBON, {
    refetchQueries: [FETCH_RIBBONS, "getRibbonsByFilter"],
  });

  const [
    updateRibbon,
    {
      data: updateRibbonData,
      loading: updateRibbonLoading,
      error: updateRibbonError,
    },
  ] = useMutation(UPDATE_RIBBON, {
    refetchQueries: [FETCH_RIBBONS, "getRibbonsByFilter"],
  });

  useMutationHandler({
    data: deleteRibbonData,
    loading: deleteRibbonLoading,
    error: deleteRibbonError,
    successMessage: "Ribbon deleted successfully",
  });

  useMutationHandler({
    data: createRibbonData,
    loading: createRibbonLoading,
    error: createRibbonError,
    successMessage: "Ribbon created successfully",
  });

  useMutationHandler({
    data: updateRibbonData,
    loading: updateRibbonLoading,
    error: updateRibbonError,
    successMessage: "Ribbon updated successfully",
  });

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const { observerRef, hasMore } = useInfiniteQueryScroll({
    items: data?.getRibbonsByFilter?.ribbons || [],
    totalCount: data?.getRibbonsByFilter?.totalCount || 0,
    loading,
    fetchMore,
    scrollContainerRef, // pass in the ref
  });

  const handleNewRibbon = () => {
    if (newRibbonName.trim()) {
      createRibbon({ variables: { name: newRibbonName } });
      setNewRibbonName("");
      setAddNewRibbon(false);
    }
  };

  // Handle edit ribbon
  const handleEditRibbon = (ribbonId: string, ribbonName: string) => {
    setEditingRibbonId(ribbonId);
    setEditingRibbonName(ribbonName);
  };

  // Handle save edited ribbon
  const handleSaveEditedRibbon = () => {
    if (editingRibbonId && editingRibbonName.trim()) {
      updateRibbon({
        variables: {
          updateRibbonId: editingRibbonId,
          input: {
            name: editingRibbonName,
          },
        },
      });
      setEditingRibbonId(null);
      setEditingRibbonName("");
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingRibbonId(null);
    setEditingRibbonName("");
  };

  if (loading && !data) return <p>Loading...</p>;
  return (
    <div className="flex flex-col gap-y-2">
      <p>
        If this product already has a ribbon, the ribbon you select here will
        replace it.
      </p>
      <Divider />
      <RadioGroup
        value={selectedRibbonId}
        onValueChange={setSelectedRibbonId}
        classNames={{ wrapper: "flex flex-col gap-y-2" }}
        className="max-h-60 overflow-y-scroll scrollbar"
        ref={scrollContainerRef} // Attach the scroll container ref here
      >
        {data?.getRibbonsByFilter?.ribbons?.map(
          (ribbon: RibbonType, index: number) => (
            <div
              key={ribbon._id}
              className="group flex w-full justify-between"
              ref={
                index === data.getRibbonsByFilter.ribbons.length - 1 && hasMore
                  ? observerRef
                  : null
              }
            >
              {editingRibbonId === ribbon._id ? (
                <div className="flex items-center gap-x-2 w-full">
                  <Input
                    classNames={{
                      inputWrapper: "rounded-md",
                      label: "text-sm",
                    }}
                    value={editingRibbonName}
                    onValueChange={setEditingRibbonName}
                    placeholder="Enter ribbon name"
                    size="sm"
                    className="flex-grow"
                  />
                  <div className="flex gap-x-2">
                    <Button
                      isIconOnly
                      startContent={<BiX className="text-2xl" />}
                      onPress={handleCancelEdit}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                    <Button
                      isIconOnly
                      startContent={<BiCheck className="text-2xl" />}
                      onPress={handleSaveEditedRibbon}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                  </div>
                </div>
              ) : (
                <>
                  <Radio value={ribbon._id}>
                    <div className="text-small">
                      {ribbon.name}{" "}
                      <small>({ribbon.products.totalCount} Products)</small>
                    </div>
                  </Radio>
                  <div className="group-hover:flex opacity-0 group-hover:opacity-100 gap-x-1">
                    <Button
                      isIconOnly
                      startContent={<BiPencil />}
                      onPress={() => handleEditRibbon(ribbon._id, ribbon.name)}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                    <Button
                      isIconOnly
                      startContent={<BiTrash />}
                      onPress={() =>
                        deleteRibbon({
                          variables: { deleteRibbonId: ribbon._id },
                        })
                      }
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                  </div>
                </>
              )}
            </div>
          )
        )}
        {loading && <Spinner className="mt-4" />}
      </RadioGroup>

      {addNewRibbon ? (
        <div className="flex items-center gap-x-2">
          <Input
            classNames={{
              inputWrapper: "rounded-md",
              label: "text-sm",
            }}
            value={newRibbonName}
            onValueChange={setNewRibbonName}
            placeholder="Enter new ribbon name"
            size="sm"
          />
          <div className="flex gap-x-2">
            <Button
              isIconOnly
              startContent={<BiX className="text-2xl" />}
              onPress={() => {
                setAddNewRibbon(false);
                setNewRibbonName("");
              }}
              className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
              size="sm"
              variant="ghost"
              radius="full"
            />
            <Button
              isIconOnly
              startContent={<BiCheck className="text-2xl" />}
              onPress={handleNewRibbon}
              className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
              size="sm"
              variant="ghost"
              radius="full"
            />
          </div>
        </div>
      ) : (
        <button
          className="text-primary text-small flex items-center gap-x-2"
          onClick={() => setAddNewRibbon(true)}
        >
          <BiPlus className="text-lg" />
          <span>Add New Ribbon</span>
        </button>
      )}
    </div>
  );
};

export default EditRibbonModal;
