import { gql } from "@apollo/client";

export const GET_INVENTORY_PRODUCTS = gql(`
query GetInventoryProducts($filters: GetInventoryProductsFilterInput, $offset: Int, $limit: Int) {
  getInventoryProducts(filters: $filters, offset: $offset, limit: $limit) {
    products {
      assets {
        _id
        path
        type
        isFeatured
        altText
      }
      _id
      categoryIds
      isDeleted
      isOnSale
      name
      price
      productType
      status
      stockQuantity
      stockStatus
      trackInventory
      categories {
        name
        imgsrc
        _id
        products {
          _id
        }
      }
    }
    totalCount
  }
}`);

export const UPDATE_INVENTORY_PRODUCT =
  gql(`mutation UpdateInventoryProduct($stockStatus: ProductStockStatusEnum,  $input: BulkUpdateInventoryProductsInput) {
  updateInventoryProduct(stockStatus: $stockStatus, input: $input )
}`);

export const BULK_UPDATE_INVENTORY_PRODUCT = gql(`
  mutation BulkUpdateInventoryProducts($input: [BulkUpdateInventoryProductsInput], $updateData: BulkUpdateInventoryProductsUpdateInput) {
  bulkUpdateInventoryProducts(input: $input, updateData: $updateData)
}`);