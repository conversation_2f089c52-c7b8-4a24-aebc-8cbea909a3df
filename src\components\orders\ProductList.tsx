import { Image } from "@heroui/react";

type ProductListProps = {
	imgsrc: string;
	productName: string;
	price: number;
	qty: number;
	finalPrice: number;
};

const ProductList = ({ imgsrc, productName, price, qty, finalPrice }: ProductListProps) => {
	return (
		<div className="grid grid-cols-2 md:grid-cols-2 w-full border-b py-4">
			<div className="flex items-center gap-2 ">
				<Image src={imgsrc} alt="product" className="w-10 h-10 rounded-md" />
				<p>{productName}</p>
			</div>
			<div className="flex items-center justify-end gap-8">
				<p>{price}</p>
				<p>x{qty}</p>
				<p className="font-semibold">{finalPrice}</p>
			</div>
		</div>
	);
};

export default ProductList;
