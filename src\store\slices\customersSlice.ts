import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import { CustomerFilters, CustomerType } from "../../types/customerType";

const INITIAL_VISIBLE_COLUMNS = [
	"firstName",
	"email",
	"phoneNumber",
	"emailSubscribedStatus",
	"lastActivity",
	"actions",
];

export interface CustomersSlice {
	customers: CustomerType[];
	setCustomers: (customers: CustomerType[]) => void;
	customerVisibleColumns: Set<string>;
	setCustomerVisibleColumns: (columns: Set<string>) => void;

	selectedCustomerKeys: Set<string> | "all";
	setSelectedCustomerKeys: (keys: Set<string> | "all") => void;

	customerFilterText: string;
	setCustomerFilterText: (text: string) => void;

	customerFilters: CustomerFilters;
	setCustomerFilters: (filters: Partial<CustomerFilters>) => void;

	selectedLabelId: string | null;
	setSelectedLabelId: (id: string | null) => void;
}

const initialState: Omit<
	CustomersSlice,
	| "setCustomerVisibleColumns"
	| "setSelectedCustomerKeys"
	| "setCustomerFilterText"
	| "setCustomerFilters"
	| "setSelectedLabelId"
	| "setCustomers"
> = {
	// Customers listing state
	customerVisibleColumns: new Set(INITIAL_VISIBLE_COLUMNS),
	selectedCustomerKeys: new Set([]),
	customerFilterText: "",
	customerFilters: {
		search: "",
		labelIds: [],
	},
	selectedLabelId: null,
	customers: [],
};

export const customersSlice: StateCreator<CustomersSlice, [], [["zustand/immer", never]]> = immer(
	(set) => ({
		...initialState,

		setCustomerVisibleColumns: (columns) => {
			set((state) => {
				state.customerVisibleColumns = columns;
			});
		},

		setSelectedCustomerKeys: (keys) => {
			set((state) => {
				state.selectedCustomerKeys = keys;
			});
		},

		setCustomerFilterText: (text) => {
			set((state) => {
				state.customerFilterText = text;
			});
		},

		setCustomerFilters: (filters) => {
			set((state) => {
				state.customerFilters = { ...state.customerFilters, ...filters };
			});
		},

		setSelectedLabelId: (id) => {
			set((state) => {
				state.selectedLabelId = id;
			});
		},

		setCustomers: (customers) => {
			set((state) => {
				state.customers = customers;
			});
		},
	})
);
