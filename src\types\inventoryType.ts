import { StatusTypes, StockStatus } from "./commonTypes";
import { CategoriesType, ProductVariantsType } from "./productType";

export interface GetInventoryType {
  _id: string;
  name: string;
  price: number;
  description: string;
  assets: { path: string }[];
  stockStatus: string;
  isDeleted: boolean;
  status: StatusTypes;
  variants: ProductVariantsType[];
  categories: CategoriesType[];
  isOnSale: boolean;
  discountedPrice: number;
  trackInventory: boolean;
  totalProductQuantity: number;
  stockQuantity: number;
}

export enum InventorySetTypeEnum {
  ADD = "ADD",
  SET = "SET",
}

export interface InventoryUpdateType {
  setQuantity: { setType: InventorySetTypeEnum; value: number };
  showAllItemsAs: StockStatus;
  trackInventory: boolean;
}
