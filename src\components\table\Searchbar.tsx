import { Input } from "@heroui/react";
import { BiSearch } from "react-icons/bi";

const SearchBar = ({
	value,
	onChange,
	onClear,
}: {
	value: string;
	onChange: (val: string) => void;
	onClear: () => void;
}) => (
	<Input
		isClearable
		size="sm"
		classNames={{ base: "md:w-72 rounded-full" }}
		radius="full"
		placeholder="Search..."
		startContent={<BiSearch />}
		value={value}
		onClear={onClear}
		onValueChange={onChange}
	/>
);

export default SearchBar;
