import { addToast, Button, Chip, useDisclosure } from "@heroui/react";
import { useCallback, useMemo, useState } from "react";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import TableComponent from "../components/Table";
import TopHeader from "../components/table/TopHeader";
import TopDrawer from "../components/TopDrawer";
import { useBoundStore } from "../store/store";
import { ColumnType, CustomerMemberStatus, CustomerType } from "../types/customerType";
import { RxDividerVertical } from "react-icons/rx";
import { dummyCustomers } from "../data/dummyCustomers";
import { BiDotsVerticalRounded, BiPencil, BiPlus, BiTag, BiTrash } from "react-icons/bi";
import { useNavigate } from "react-router-dom";
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { IoEyeOutline } from "react-icons/io5";
import { FiAlertTriangle } from "react-icons/fi";
import { useCustomerModals } from "../hooks/useCustomerModals";
import ModalComponent from "../components/ModalComponent";
import EditLabelsModal from "../components/customers/EditLabelsModal";
import CustomerFormModal from "../components/customers/CustomerFormModal";
import { useCustomerFormModal } from "../hooks/useCustomerFormModal";
import { useMutation, useQuery } from "@apollo/client";
import { UPDATE_LABEL, GET_CUSTOMERS, DELETE_CUSTOMERS } from "../graphql/customers";
import { useMutationHandler } from "../hooks/useMutationStatusHandler";

// Define columns for the customers table
export const columns: ColumnType[] = [
	{ name: "Name", uid: "firstName", sortable: true },
	{ name: "Email", uid: "email", sortable: true },
	{ name: "Phone", uid: "phoneNumber", sortable: false },
	{ name: "Member status", uid: "emailSubscribedStatus", sortable: false },
	{ name: "Last activity", uid: "lastActivity", sortable: true },
	{ name: "ACTIONS", uid: "actions", sortable: false },
];

const Customers = () => {
	const {
		setCustomers,
		customers,
		customerVisibleColumns,
		setCustomerVisibleColumns,
		selectedCustomerKeys,
		setSelectedCustomerKeys,
		customerFilterText,
		setCustomerFilterText,
		selectedLabelId,
		setSelectedLabelId,
	} = useBoundStore();
	const { isOpen, onOpenChange, onOpen } = useDisclosure();
	const navigate = useNavigate();

	// Use the custom hook to manage all modal states
	const { confirmationModal, bulkDeleteModal, addLabelsModal } = useCustomerModals();
	const { data, loading, error, fetchMore, refetch } = useQuery(GET_CUSTOMERS, {
		variables: {
			limit: 10,
			offset: 0,
			filters: {
				search: customerFilterText,
				labelIds: null,
			},
		},
		fetchPolicy: "cache-and-network", // use 'cache-and-network' or 'network-only'
		notifyOnNetworkStatusChange: true,
		onCompleted: (data) => {
			if (data && data.getUsersByFilters && data.getUsersByFilters.data) {
				setCustomers(data.getUsersByFilters.data);
			}
		},
	});

	if (!loading && error) {
		addToast({
			title: "Failed to load customers",
			color: "danger",
			shouldShowTimeoutProgress: true,
		});
	}
	const totalCount = data?.getUsersByFilters?.totalCount || 0;
	const hasMore = customers.length < totalCount;

	const handleLoadMore = () => {
		if (!hasMore || loading) return;
		fetchMore({
			variables: {
				offset: customers.length || 0,
				limit: 10,
				filters: { search: "" },
			},
			updateQuery: (prev, { fetchMoreResult }) => {
				if (!fetchMoreResult) return prev;
				const mergedCustomers = [
					...prev.getUsersByFilters.products,
					...fetchMoreResult.getUsersByFilters.products,
				];
				setCustomers(mergedCustomers);

				return {
					getProducts: {
						__typename: prev.getUsersByFilters.__typename,
						customers: mergedCustomers,
						totalCount: fetchMoreResult.getUsersByFilters.totalCount,
					},
				};
			},
		});
	};

	// Update label mutation
	const [
		updateLabel,
		{ data: updateLabelData, loading: updateLabelLoading, error: updateLabelError },
	] = useMutation(UPDATE_LABEL);

	// delete the admin side user
	const [deleteAdminSideUser, { data: deleteData, loading: deleteLoading, error: deleteError }] =
		useMutation(DELETE_CUSTOMERS, {
			refetchQueries: [GET_CUSTOMERS, "GetUsersByFilters"],
		});

	useMutationHandler({
		data: updateLabelData,
		loading: updateLabelLoading,
		error: updateLabelError,
		successMessage: "Labels applied to customers successfully",
		onClose: addLabelsModal.onClose,
	});

	useMutationHandler({
		data: deleteData,
		loading: deleteLoading,
		error: deleteError,
		successMessage: "Customer deleted successfully",
		onClose: confirmationModal.onClose,
	});

	// Use the customer form modal hook
	const { customerFormModal, customerData, isEditMode, openCreateModal, openEditModal } =
		useCustomerFormModal();

	console.log(customerData, "customerData");

	// State for tracking which customer to delete (for single delete)
	const [deleteCustomerId, setDeleteCustomerId] = useState("");

	const handleAction = () => {
		refetch({
			filters: {
				search: customerFilterText,
				labelIds: null,
			},
		});
	};

	// Render cell content based on column key
	const renderCell = useCallback(
		(customer: CustomerType, columnKey: keyof CustomerType | string) => {
			const cellValue = customer[columnKey as keyof CustomerType];

			switch (columnKey) {
				case "firstName":
					return (
						<div className="flex flex-col">
							<p className="text-bold text-small capitalize">{`${customer.firstName} ${customer.lastName}`}</p>
						</div>
					);
				case "email":
					return (
						<div className="flex flex-col">
							<p className="text-bold text-small">{customer.email}</p>
							<small className="text-textPlaceHolder uppercase">
								{customer.subscriptionStatus}
							</small>
						</div>
					);
				case "phoneNumber":
					return (
						<div className="flex flex-col">
							<p className="text-bold text-small">{customer.phoneNumber || "-"}</p>
						</div>
					);
				case "emailSubscribedStatus":
					return (
						<Chip
							className="capitalize"
							color={
								customer.emailSubscribedStatus === CustomerMemberStatus.SUBSCRIBED
									? "success"
									: "default"
							}
							size="sm"
							variant="flat"
						>
							{customer.emailSubscribedStatus === CustomerMemberStatus.SITE_MEMBER
								? "Site Member"
								: customer.emailSubscribedStatus === CustomerMemberStatus.SUBSCRIBED
								? "Subscribed"
								: "Never Subscribed"}
						</Chip>
					);
				case "lastActivity":
					return (
						<div className="flex flex-col">
							<p className="text-bold text-small">{customer.lastActivity}</p>
						</div>
					);
				case "actions":
					return (
						<div className="flex justify-end items-center">
							<Dropdown>
								<DropdownTrigger>
									<Button isIconOnly variant="light" size="sm">
										<BiDotsVerticalRounded className="text-xl" />
									</Button>
								</DropdownTrigger>
								<DropdownMenu aria-label="Customer Actions">
									<DropdownItem
										key="view"
										startContent={<IoEyeOutline />}
										onPress={() => navigate(`/admin/customers/${customer._id}`)}
									>
										View
									</DropdownItem>
									<DropdownItem
										key="edit"
										startContent={<BiPencil />}
										onPress={() => {
											openEditModal(customer);
										}}
									>
										Edit
									</DropdownItem>
									<DropdownItem
										key="delete"
										startContent={<BiTrash />}
										className="text-danger"
										color="danger"
										onPress={() => {
											setDeleteCustomerId(customer._id);
											confirmationModal.onOpen();
										}}
									>
										Delete
									</DropdownItem>
								</DropdownMenu>
							</Dropdown>
						</div>
					);
				default:
					return typeof cellValue === "object" ? JSON.stringify(cellValue) : cellValue;
			}
		},
		[]
	);

	// We can use this later when implementing bulk actions
	const finalSelectedKeys = useMemo(() => {
		if (selectedCustomerKeys === "all") {
			return customers.map((item: CustomerType) => item._id);
		}
		return Array.from(selectedCustomerKeys.values());
	}, [selectedCustomerKeys]);

	// Top content for the table (search, filters, etc.)
	const topContent = useMemo(() => {
		return (
			<TopHeader
				columns={columns}
				filterValue={customerFilterText}
				onOpen={onOpen}
				onSearchChange={setCustomerFilterText}
				setVisibleColumns={(cols) => setCustomerVisibleColumns(cols)}
				visibleColumns={customerVisibleColumns}
				showItems={{
					columnsToggle: true,
					exportImportButton: false,
					filters: true,
					searchBar: true,
				}}
			/>
		);
	}, [customerFilterText, customerVisibleColumns, columns]);

	// Top content when rows are selected
	const topSelectedContent = useMemo(() => {
		return (
			<div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 p-2 rounded-md">
				<p>
					{selectedCustomerKeys === "all" // API_CHANGE will need to add the change here
						? dummyCustomers.length
						: selectedCustomerKeys.size}{" "}
					of {dummyCustomers.length} Selected
				</p>
				<RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
				<div className="flex gap-x-3">
					<Button
						color="primary"
						radius="full"
						size="sm"
						variant="ghost"
						className="border"
						startContent={<BiTag />}
						onPress={() => addLabelsModal.onOpen()}
					>
						Add Labels
					</Button>
					{/* <Button
            color="primary"
            radius="full"
            size="sm"
            variant="flat"
            startContent={<MdOutlineEmail />}
            onPress={() => sendEmailModal.onOpen()}
          >
            Send Email Campaign
          </Button> */}
					<Button
						color="primary"
						radius="full"
						size="sm"
						variant="ghost"
						className="border"
						startContent={<BiTrash />}
						onPress={() => bulkDeleteModal.onOpen()}
					>
						Delete
					</Button>
				</div>
			</div>
		);
	}, [selectedCustomerKeys]);

	return (
		<>
			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<Breadcrumbs>
					<BreadcrumbItem>Dashboard</BreadcrumbItem>
					<BreadcrumbItem>Customers</BreadcrumbItem>
				</Breadcrumbs>
			</div>

			{/* Single customer delete confirmation modal */}
			<ModalComponent
				isOpen={confirmationModal.isOpen}
				onOpenChange={confirmationModal.onOpenChange}
				id="customer-modal-2"
				onPress={() => {
					// API_CHANGE: Add delete customer API call here
					deleteAdminSideUser({ variables: { deleteAdminSideUserId: deleteCustomerId } });
				}}
				modalHeader={"Delete Customer"}
				saveButtonText={"Delete"}
				className={{
					saveColor: "danger",
				}}
				isLoading={deleteLoading}
			>
				<div className="flex flex-col items-center gap-y-2">
					<FiAlertTriangle className="text-3xl text-red-500" />
					<h5>Are you sure you want to delete this customer?</h5>
				</div>
			</ModalComponent>

			{/* Bulk delete confirmation modal */}
			<ModalComponent
				isOpen={bulkDeleteModal.isOpen}
				onOpenChange={bulkDeleteModal.onOpenChange}
				id="customer-modal-3"
				onPress={() => {
					// API_CHANGE: Add bulk delete customers API call here
					console.log(`Deleting ${finalSelectedKeys.length} customers`);
					bulkDeleteModal.onClose();
				}}
				modalHeader={"Delete Customers"}
				saveButtonText={"Delete"}
				className={{
					saveColor: "danger",
				}}
				isLoading={false}
			>
				<div className="flex flex-col items-center gap-y-2">
					<FiAlertTriangle className="text-3xl text-red-500" />
					<h5>Are you sure you want to delete the selected customers?</h5>
				</div>
			</ModalComponent>

			{/* Add labels modal */}
			<ModalComponent
				isOpen={addLabelsModal.isOpen}
				onOpenChange={() => {
					// Reset selectedLabelId when modal is closed
					if (!addLabelsModal.isOpen) {
						setSelectedLabelId(null);
					}
					addLabelsModal.onOpenChange();
				}}
				id="customer-modal-4"
				onPress={() => {
					// Apply labels to selected customers
					if (selectedLabelId && finalSelectedKeys.length > 0) {
						updateLabel({
							variables: {
								updateLabelId: selectedLabelId,
								input: {
									customerIds: finalSelectedKeys,
								},
							},
						}).then(() => {
							addLabelsModal.onClose();
							setSelectedLabelId(null);
						});
					}
				}}
				modalHeader={"Add Labels"}
				saveButtonText={"Apply"}
				isLoading={false}
				disabled={!selectedLabelId} // Disable Apply button until a label is selected
			>
				<EditLabelsModal />
			</ModalComponent>

			{/* Send email campaign modal */}
			{/* <ModalComponent
        isOpen={sendEmailModal.isOpen}
        onOpenChange={sendEmailModal.onOpenChange}
        id="customer-modal-5"
        onPress={() => {
          // API_CHANGE: Add send email campaign API call here
          console.log(`Sending email to ${finalSelectedKeys.length} customers`);
          sendEmailModal.onClose();
        }}
        modalHeader={"Send Email Campaign"}
        saveButtonText={"Send"}
        isLoading={false}
      >
        <div className="p-4">
          <p>Email campaign selection UI will go here</p>
        </div>
      </ModalComponent> */}

			<TopDrawer
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				drawerHeader="Filters"
				isLoading={false}
				handleAction={handleAction}
			>
				{/* API_CHANGE Customer filters will go here */}
				<div className="p-4">
					<p>Customer filters coming soon</p>
				</div>
			</TopDrawer>

			<div className="flex justify-between items-center w-full mb-5 mt-2">
				<h1>Customers ({dummyCustomers.length})</h1>
				<Button
					size="sm"
					radius="full"
					color="primary"
					startContent={<BiPlus />}
					onPress={openCreateModal}
				>
					Add Customer
				</Button>
			</div>

			<TableComponent<CustomerType>
				columns={columns}
				renderCell={renderCell}
				topContent={topContent}
				topSelectedContent={topSelectedContent}
				list={{ items: customers }} //API_CHANGE replace this with the actual data
				visibleColumns={customerVisibleColumns}
				isLoading={loading}
				hasMore={hasMore}
				selectedKeys={selectedCustomerKeys}
				setSelectedKeys={setSelectedCustomerKeys}
				handleLoadMore={handleLoadMore}
				tableClassName="paymentsTable"
			/>

			{/* Customer Form Modal */}
			<CustomerFormModal
				isOpen={customerFormModal.isOpen}
				onOpenChange={customerFormModal.onOpenChange}
				initialData={customerData}
				isEditMode={isEditMode}
			/>
		</>
	);
};

export default Customers;
