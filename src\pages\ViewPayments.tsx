import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  BreadcrumbItem,
  <PERSON>readcrumbs,
  Button,
  Chip,
  Spinner,
} from "@heroui/react";
import CardWrapper from "../components/CardWrapper";
// We'll use the store when implementing API integration
import { useEffect, useState } from "react";
import { PaymentStatus, PaymentType } from "../types/paymentTypes";
import { dummyPayments } from "../data/dummyPayments";
import { BiChevronRight } from "react-icons/bi";

const ViewPayments = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [paymentData, setPaymentData] = useState<PaymentType | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // In a real implementation, this would be replaced with an API call
  useEffect(() => {
    // Simulate API call with timeout
    const timer = setTimeout(() => {
      // Find the payment with the matching ID from dummy data
      const payment = dummyPayments.find((p) => p._id === id);
      setPaymentData(payment || null);
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [id]);

  if (isLoading) {
    return (
      <div className="p-4 flex items-center justify-center w-full h-screen">
        <Spinner className="text-xl" />
      </div>
    );
  }

  if (!paymentData) {
    return <div className="p-4">Payment not found</div>;
  }

  // Format the payment amount
  const formattedAmount = `₹${paymentData.amount.toFixed(2)}`;

  return (
    <div className="pb-10">
      {/* Breadcrumbs and header */}
      <div className="grid py-2 grid-cols-2 justify-end items-center border-b mb-5 ml-1 sticky top-[3.15rem] bg-white dark:bg-transparent left-0 z-30">
        <div className="md:px-3">
          <Breadcrumbs>
            <BreadcrumbItem onClick={() => navigate("/admin/sales/payments")}>
              Payments
            </BreadcrumbItem>
            <BreadcrumbItem>{formattedAmount} Payment</BreadcrumbItem>
          </Breadcrumbs>
        </div>
        <div className="flex gap-x-4 w-full justify-end">
          <Button
            size="sm"
            radius="full"
            color="primary"
            variant="ghost"
            onPress={() => navigate("/admin/sales/payments")}
          >
            Back
          </Button>
        </div>
      </div>

      {/* Main content */}
      <h1 className="text-2xl font-semibold mb-4 pl-2 md:pl-1">
        {formattedAmount} Payment
        <Chip
          className="ml-3 capitalize"
          color={
            paymentData.status === PaymentStatus.SUCCESSFUL
              ? "success"
              : "danger"
          }
          size="sm"
          variant="flat"
        >
          {paymentData.status.toLowerCase()}
        </Chip>
      </h1>

      {/* Payment amount card */}
      <CardWrapper>
        <div className="flex justify-between items-center">
          <div className="text-2xl font-semibold">{formattedAmount}</div>
          <Chip
            className="capitalize"
            color={
              paymentData.status === PaymentStatus.SUCCESSFUL
                ? "success"
                : "danger"
            }
            size="sm"
            variant="flat"
          >
            {paymentData.status.toLowerCase()}
          </Chip>
          {/* Functionality for this refund will be added soon */}
          <Button size="sm" radius="full" color="default" variant="flat">
            Refund
          </Button>
        </div>
      </CardWrapper>

      {/* Payment details grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-5 dark:text-white">
        {/* Left column */}
        <div className="space-y-4">
          {/* Payment info */}
          <CardWrapper title="Payment details">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Amount</div>
                <div className="font-medium">{formattedAmount}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Status</div>
                <div>
                  <Chip
                    className="capitalize"
                    color={
                      paymentData.status === PaymentStatus.SUCCESSFUL
                        ? "success"
                        : "danger"
                    }
                    size="sm"
                    variant="flat"
                  >
                    {paymentData.status.toLowerCase()}
                  </Chip>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  Payment type
                </div>
                <div className="font-medium">Single payment</div>
              </div>
            </div>
          </CardWrapper>

          {/* Payment method */}
          <CardWrapper title="Payment method">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  Payment provider
                </div>
                <div className="font-medium">{paymentData.paymentMethod}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  Your merchant account
                </div>
                <div className="font-medium"></div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Method</div>
                <div className="font-medium">{paymentData.paymentMethod}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  {paymentData.paymentMethod} payment ID
                </div>
                <div className="font-medium flex items-center">
                  ********-c0ed-4bf2-b56e-54283d418423
                  <Button isIconOnly size="sm" variant="light" className="ml-1">
                    <BiChevronRight />
                  </Button>
                </div>
              </div>
            </div>
          </CardWrapper>

          {/* Billing details */}
          <CardWrapper title="Billing details">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  Billing name
                </div>
                <div className="font-medium">{paymentData.customer.name}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  Billing address
                </div>
                <div className="font-medium">
                  Mangal vatika Society near Raj residency Society, Chharwada
                  road, Balitha, Vapi, Valsad, Gujarat - 396191, Vapi, GJ,
                  396191, India
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Email</div>
                <div className="font-medium">{paymentData.customer.email}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Phone</div>
                <div className="font-medium">
                  {paymentData.customer.phone || "N/A"}
                </div>
              </div>
            </div>
          </CardWrapper>
        </div>

        {/* Right column */}
        <div className="space-y-4">
          {/* Order details */}
          <CardWrapper
            title={`Order #10243 details`}
            endContent={
              <Button
                size="sm"
                variant="light"
                onPress={() => navigate("/admin/sales/orders/10243")}
              >
                View Order
              </Button>
            }
          >
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">
                  Products or services
                </div>
                <div></div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Name</div>
                <div className="font-medium">Omkar Hankare</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Address</div>
                <div className="font-medium">
                  Mangal vatika Society near Raj residency Society, Chharwada
                  road, Balitha, Vapi, Valsad, Gujarat - 396191, Vapi, GJ,
                  396191, India
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Email</div>
                <div className="font-medium">{paymentData.customer.email}</div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="text-gray-600 dark:text-white">Phone</div>
                <div className="font-medium">
                  {paymentData.customer.phone || "N/A"}
                </div>
              </div>
            </div>

            {/* Products list */}
            <div className="mt-6">
              <div className="text-gray-600 dark:text-white mb-2">
                Products or services
              </div>
              <div className="border-b pb-4 mb-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    {/* <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                        <Image
                          src="/images/placeholder.svg"
                          alt="Product"
                          className="w-10 h-10 object-contain"
                        />
                      </div> */}
                    <div>
                      <div className="font-medium">Gojo Mug Colour:Black</div>
                      <div className="text-sm text-gray-500">QTY: 1</div>
                    </div>
                  </div>
                  <div className="font-medium">₹249.00</div>
                </div>
              </div>
              <div className="border-b pb-4 mb-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    {/* <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                        <Image
                          src="/images/placeholder.svg"
                          alt="Product"
                          className="w-10 h-10 object-contain"
                        />
                      </div> */}
                    <div>
                      <div className="font-medium">Gojo Bobble head</div>
                      <div className="text-sm text-gray-500">QTY: 1</div>
                    </div>
                  </div>
                  <div className="font-medium">₹349.00</div>
                </div>
              </div>

              {/* Shipping */}
              <div className="border-b pb-4 mb-4">
                <div className="flex justify-between items-center">
                  <div>Shipping</div>
                  <div className="font-medium">₹75.00</div>
                </div>
              </div>

              {/* Total */}
              <div className="flex justify-between items-center font-semibold">
                <div>Total amount</div>
                <div>{formattedAmount}</div>
              </div>
            </div>
          </CardWrapper>

          {/* Payment history */}
          <CardWrapper title="Payment history">
            <div className="flex items-center gap-3 py-2">
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
              <div className="text-gray-600 dark:text-white">
                4 April 2025, 02:00 pm
              </div>
              <Chip
                className="capitalize"
                color={
                  paymentData.status === PaymentStatus.SUCCESSFUL
                    ? "success"
                    : "danger"
                }
                size="sm"
                variant="flat"
              >
                {paymentData.status.toLowerCase()}
              </Chip>
              <div>Payment successful</div>
            </div>
          </CardWrapper>
        </div>
      </div>
    </div>
  );
};

export default ViewPayments;
