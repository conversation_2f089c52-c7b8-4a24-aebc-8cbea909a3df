import { useDisclosure } from "@heroui/react";

/**
 * Custom hook to manage all modals in the Orders page
 */
export const useOrderModals = () => {
  // Main filter modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure({
    id: "order-modal-1",
  });

  // Delete confirmation modal for single order
  const {
    isOpen: isConfirmationOpen,
    onOpen: onConfirmationOpen,
    onClose: onConfirmationClose,
    onOpenChange: onConfirmationOpenChange,
  } = useDisclosure({ id: "order-modal-2" });

  // Bulk delete orders modal
  const {
    isOpen: isBulkDeleteOpen,
    onOpen: onBulkDeleteOpen,
    onClose: onBulkDeleteClose,
    onOpenChange: onBulkDeleteOpenChange,
  } = useDisclosure({ id: "order-modal-3" });

  // Order tags modal
  const {
    isOpen: isOrderTagsOpen,
    onOpen: onOrderTagsOpen,
    onClose: onOrderTagsClose,
    onOpenChange: onOrderTagsOpenChange,
  } = useDisclosure({ id: "order-modal-4" });

  return {
    // Main filter modal
    mainModal: {
      isOpen,
      onOpen,
      onOpenChange,
      onClose,
    },

    // Delete confirmation modal
    confirmationModal: {
      isOpen: isConfirmationOpen,
      onOpen: onConfirmationOpen,
      onClose: onConfirmationClose,
      onOpenChange: onConfirmationOpenChange,
    },

    // Bulk delete orders modal
    bulkDeleteModal: {
      isOpen: isBulkDeleteOpen,
      onOpen: onBulkDeleteOpen,
      onClose: onBulkDeleteClose,
      onOpenChange: onBulkDeleteOpenChange,
    },

    // Order tags modal
    orderTagsModal: {
      isOpen: isOrderTagsOpen,
      onOpen: onOrderTagsOpen,
      onClose: onOrderTagsClose,
      onOpenChange: onOrderTagsOpenChange,
    },
  };
};
