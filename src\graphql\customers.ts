import { gql } from "@apollo/client";

export const CHECK_LABEL = gql`
	query CheckLabel($checkLabelId: ID!) {
		checkLabel(id: $checkLabelId) {
			_id
			name
		}
	}
`;

export const GET_LABEL_BY_ID = gql`
	query GetLabelById($getLabelByIdId: ID!) {
		getLabelById(id: $getLabelByIdId) {
			_id
			name
		}
	}
`;

export const GET_LABELS = gql`
	query GetLabelsByFilter($limit: Int, $offset: Int, $filters: GetLabelsFiltersInput) {
		getLabelsByFilter(limit: $limit, offset: $offset, filters: $filters) {
			data {
				_id
				name
				isDefault
			}
			totalCount
		}
	}
`;

export const CREATE_LABEL = gql`
	mutation CreateLabel($name: String!, $isDefault: Boolean) {
		createLabel(name: $name, isDefault: $isDefault) {
			_id
			name
		}
	}
`;

export const DELETE_LABEL = gql`
	mutation DeleteLabel($deleteLabelId: ID!) {
		deleteLabel(id: $deleteLabelId)
	}
`;

export const UPDATE_LABEL = gql`
	mutation UpdateLabel($updateLabelId: ID!, $input: UpdateLabelsInput) {
		updateLabel(id: $updateLabelId, input: $input) {
			_id
			name
		}
	}
`;

export const SAVE_CUSTOMERS = gql`
	mutation SaveAdminSideUser($input: CreateUserInput!, $saveAdminSideUserId: ID) {
		saveAdminSideUser(input: $input, id: $saveAdminSideUserId) {
			_id
		}
	}
`;

export const GET_CUSTOMERS = gql`
	query GetUsersByFilters($filters: GetUsersFiltersInput, $limit: Int, $offset: Int) {
		getUsersByFilters(filters: $filters, limit: $limit, offset: $offset) {
			totalCount
			data {
				_id
				firstName
				lastName
				addresses {
					_id
					userId
					addressType
					flat
					addressline1
					addressline2
					landmark
					countryCode
					phone
					city
					country
					states
					pincode
					primary
					createdAt
					updatedAt
				}
				profileImg
				gender
				email
				phoneNumber
				countryCode
				labelIds
				label {
					_id
					name
				}
				dateOfBirth
				lastActive
				emailSubscribedStatus
				status
				isDeleted
				createdAt
				updatedAt
			}
		}
	}
`;

export const DELETE_CUSTOMERS = gql`
	mutation DeleteAdminSideUser($deleteAdminSideUserId: ID) {
		deleteAdminSideUser(id: $deleteAdminSideUserId)
	}
`;
