import { Input, Select, SelectItem } from "@heroui/react";
import { ChangePriceType } from "../../types/productType";
import { LiaRupeeSignSolid } from "react-icons/lia";
import { useBoundStore } from "../../store/store";

const changePriceOptions: { value: string; label: string }[] = [
  { value: "INCREASE_BY_AMOUNT", label: "Increase by amount" },
  { value: "REDUCE_BY_AMOUNT", label: "Reduce by amount" },
  { value: "SET_A_NEW_PRICE", label: "Set a new price" },
  {
    value: "INCREASE_BY_PERCENTAGE",
    label: "Increase by percentage",
  },
  {
    value: "REDUCE_BY_PERCENTAGE",
    label: "Reduce by percentage",
  },
];
const ChangeBulkPriceModal = () => {
  const { changePriceInputs, setChangePriceType, setChangePriceValue } =
    useBoundStore();

  console.log(changePriceOptions, " change price options");
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 place-items-center gap-4 w-full">
      <Select
        labelPlacement="outside"
        className="max-w-xs"
        classNames={{
          trigger: "rounded-md",
        }}
        label="What change would you like to make?"
        placeholder="Select"
        value={
          changePriceInputs.type ? changePriceInputs.type.toString() : undefined
        }
        onChange={(e) => setChangePriceType(e.target.value)}
      >
        {changePriceOptions.map((animal) => (
          <SelectItem key={animal.value}>{animal.label}</SelectItem>
        ))}
      </Select>
      <Input
        label="Set"
        size="sm"
        value={
          isNaN(changePriceInputs.value ?? NaN)
            ? ""
            : String(changePriceInputs.value)
        }
        onValueChange={(value) => setChangePriceValue(parseFloat(value))}
        startContent={
          <LiaRupeeSignSolid className="text-primary font-medium text-xl" />
        }
        isRequired
        labelPlacement="outside"
        classNames={{
          inputWrapper: "w-full h-10 after:h-[1px] after:bg-primary rounded-md",
        }}
        placeholder="e.g., 100"
      />
    </div>
  );
};

export default ChangeBulkPriceModal;
