import { gql } from "@apollo/client";

export const FETCH_ORDERS = gql`
	query GetOrdersByFilters($filter: OrderFilterInput, $offset: Int, $limit: Int) {
		getOrdersByFilters(filter: $filter, offset: $offset, limit: $limit) {
			data {
				_id
				tagIds
				orderNo
				orderPrice
				paymentStatus
				currency
				fulfillmentStatus
				userData {
					firstName
					lastName
					userId
				}
				itemcount
				totalprice
				createdAt
			}
			totalCount
		}
	}
`;
export const FETCH_ORDER_BY_ID = gql`
	query GetOrderById($getOrderByIdId: ID!) {
		getOrderById(id: $getOrderByIdId) {
			_id
			billingAddress {
				addressline1
				addressline2
				addressType
				city
				country
				flat
				landmark
				phone
				states
				pincode
				primary
			}
			billingAddressId
			coupon {
				couponCode
				couponName
				couponId
			}
			createdAt
			customDiscount {
				amount
				discountReason
			}
			customFee {
				amount
				name
			}
			discountedPrice
			fulfillmentStatus
			itemcount
			orderNo
			orderPrice
			paymentStatus
			shippingAddress {
				addressType
				flat
				addressline1
				addressline2
				landmark
				phone
				city
				country
				states
				pincode
				primary
			}
			shippingAddressId
			shippingAmount
			tagIds
			taxAmount
			totalDiscount
			totalprice
			userData {
				firstName
				lastName
				email
				phone
			}
			cart {
				asset {
					path
					_id
					altText
					type
				}
				finalPrice
				name
				price
				productId
				qty
			}
		}
	}
`;
