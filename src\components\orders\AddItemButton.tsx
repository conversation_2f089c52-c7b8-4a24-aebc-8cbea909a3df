import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
} from "@heroui/react";
import { BiChevronDown, BiPlus } from "react-icons/bi";
import { BsCalendar4Week } from "react-icons/bs";
import { SlTag } from "react-icons/sl";

type AddItemButtonProps = {
  handleAddProduct: () => void;
  handleAddService: () => void;
  handleAddCustomItem: () => void;
};

const AddItemButton = ({
  handleAddProduct,
  handleAddService,
  handleAddCustomItem,
}: AddItemButtonProps) => {
  return (
    <Dropdown>
      <DropdownTrigger className="flex !rounded-[40px]">
        <Button
          startContent={<BiPlus className="text-small " />}
          endContent={<BiChevronDown className="text-small " />}
          variant="ghost"
          size="sm"
          color="primary"
          className="border"
        >
          Add Item
        </Button>
      </DropdownTrigger>
      <DropdownMenu disallowEmptySelection closeOnSelect={true}>
        {/* <DropdownSection showDivider> */}
        <DropdownItem
          className="capitalize hover:dark:bg-slate-700 hover:dark:text-white"
          classNames={{
            base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
            wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
          }}
          startContent={<SlTag />}
          key="product"
          onPress={handleAddProduct}
        >
          Product
        </DropdownItem>
        <DropdownItem
          startContent={<BsCalendar4Week />}
          onPress={handleAddService}
          key="2"
          className="capitalize hover:dark:bg-slate-700 hover:dark:text-white"
          classNames={{
            base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
            wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
          }}
        >
          Service
        </DropdownItem>
        {/* </DropdownSection> */}
        <DropdownSection>
          <DropdownItem
            onPress={handleAddCustomItem}
            startContent={<BiPlus />}
            key="3"
            className="capitalize hover:dark:bg-slate-700 hover:dark:text-white"
            classNames={{
              base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
              wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
            }}
          >
            Add Custom Item
          </DropdownItem>
        </DropdownSection>
      </DropdownMenu>
    </Dropdown>
  );
};

export default AddItemButton;
