import * as z from "zod";
import { CustomerMemberStatus } from "../types/customerType";

export const customerValidationSchema = z.object({
	firstName: z.string().min(1, { message: "First name is required" }),
	lastName: z.string().min(1, { message: "Last name is required" }),
	email: z.string().email({ message: "Invalid email address" }),
	additionalEmails: z
		.array(
			z.object({
				email: z.string().email({ message: "Invalid email address" }),
				type: z.string(),
				isSubscribed: z.boolean().optional(),
			})
		)
		.optional(),
	phoneNumber: z.string(),
	countryCode: z.string(),
	gender: z.enum(["MALE", "FEMALE", "OTHER"]),
	dateOfBirth: z.string().optional(),
	additionalPhones: z
		.array(
			z.object({
				phone: z.string(),
				type: z.string(),
				isSubscribed: z.boolean().optional(),
			})
		)
		.optional(),
	addresses: z
		.array(
			z.object({
				_id: z.string().optional(),
				userId: z.string().optional(),
				addressType: z.enum(["WORK", "HOME", "BILLING", "SHIPPING", "OTHER"]),
				flat: z.number(),
				addressline1: z.string(),
				addressline2: z.string().optional(),
				landmark: z.string().optional(),
				countryCode: z.string().optional(),
				phone: z.string().optional(),
				city: z.string(),
				country: z.string(),
				states: z.string(),
				pincode: z.string(),
				primary: z.boolean(),
				createdAt: z.string().optional(),
				updatedAt: z.string().optional(),
			})
		)
		.optional(),
	company: z.string().optional(),
	position: z.string().optional(),
	language: z.string().optional(),
	vatId: z.string().optional(),
	birthdate: z.string().optional(),
	customFields: z.record(z.string()).optional(),
	emailSubscribedStatus: z.nativeEnum(CustomerMemberStatus).optional(),
	assignee: z.string().optional(),
	labelIds: z.array(z.string()).optional(),
});

export type CustomerFormData = z.infer<typeof customerValidationSchema>;
