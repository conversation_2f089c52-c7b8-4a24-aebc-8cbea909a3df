import React, { useEffect, useRef, useState } from "react";
import { useQuery } from "@apollo/client";
import { GET_PRODUCTS } from "../../graphql/products";
import { useInfiniteQueryScroll } from "../../hooks/useInfiniteQueryScroll";
import { GetProductsType, ProductOptions, ProductVariantsType } from "../../types/productType";
import { addToast, Image, Input, Spinner, Button, Select, SelectItem } from "@heroui/react";
import { RadioGroup } from "@heroui/react";
import { CiImageOn } from "react-icons/ci";
import { CustomRadio } from "../forms/CustomRadio";
import { BiSearch, BiLeftArrow } from "react-icons/bi";
import { useBoundStore } from "../../store/store";
import { StatusTypes } from "../../types/commonTypes";

type SelectProductTypes = {
	productId: string | null;
	setProductId: (value: string) => void;
};

// Step types for the multi-step process
type Step = "product-selection" | "variant-selection" | "options-selection";

interface SelectedOptions {
	[optionName: string]: string;
}

const SelectProduct = ({ productId, setProductId }: SelectProductTypes) => {
	const [filterValue, setFilterValue] = useState<string>("");
	const [currentStep, setCurrentStep] = useState<Step>("product-selection");
	const [selectedProduct, setSelectedProduct] = useState<GetProductsType | null>(null);
	const [selectedVariant, setSelectedVariant] = useState<ProductVariantsType | null>(null);
	const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({});
	const inputRef = useRef<HTMLInputElement>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);
	const { setCartItem } = useBoundStore();

	const { data, loading, error, fetchMore, refetch } = useQuery(GET_PRODUCTS, {
		variables: {
			offset: 0,
			limit: 10,
			filters: {},
		},
		notifyOnNetworkStatusChange: true,
	});

	console.log(data, " data products");

	const { observerRef } = useInfiniteQueryScroll({
		items: data?.getProducts?.products || [],
		totalCount: data?.getProducts?.totalCount || 0,
		loading,
		fetchMore,
		scrollContainerRef,
	});

	// Refetch products whenever the filterValue changes
	useEffect(() => {
		const timeoutId = setTimeout(() => {
			refetch({
				offset: 0,
				limit: 10,
				filters: {
					search: filterValue,
				},
			});
		}, 300); // Debounce the search to avoid excessive queries
		return () => clearTimeout(timeoutId);
	}, [filterValue, refetch]);

	// Restore focus to the input after refetch
	useEffect(() => {
		if (inputRef.current) {
			inputRef.current.focus();
		}
	}, [data]);

	//   if (loading && !data) return <Spinner className="mt-2" />;
	if (error) {
		addToast({
			title: "Error in fetching products",
			shouldShowTimeoutProgress: true,
			timeout: 2000,
			color: "danger",
		});
		return;
	}

	const onClear = () => {
		setFilterValue("");
	};

	// Helper function to determine next step after product selection
	const determineNextStep = (product: GetProductsType): Step => {
		// Check if product has variants with PUBLISHED status
		const publishedVariants = product.variants?.filter(
			(variant) => variant.status === StatusTypes.PUBLISHED
		);

		if (publishedVariants && publishedVariants.length > 0) {
			return "variant-selection";
		}

		// Check if product has productOptions
		if (product.productOptions && product.productOptions.length > 0) {
			return "options-selection";
		}

		// No variants or options, product can be added directly
		return "product-selection";
	};

	// Handle product selection
	const handleProductSelect = (value: string) => {
		setProductId(value);
		const product = data.getProducts.products.find(
			(product: GetProductsType) => product._id === value
		);

		if (product) {
			setSelectedProduct(product);
			const nextStep = determineNextStep(product);

			if (nextStep === "product-selection") {
				// Product can be added directly
				setCartItem(product);
			} else {
				setCurrentStep(nextStep);
			}
		}
	};

	// Handle variant selection
	const handleVariantSelect = (variant: ProductVariantsType) => {
		setSelectedVariant(variant);

		// Check if product has productOptions after variant selection
		if (selectedProduct?.productOptions && selectedProduct.productOptions.length > 0) {
			setCurrentStep("options-selection");
		} else {
			// Create cart item with selected variant
			createCartItemWithVariant(variant);
		}
	};

	// Handle option selection
	const handleOptionChange = (optionName: string, value: string) => {
		setSelectedOptions((prev) => ({
			...prev,
			[optionName]: value,
		}));
	};

	// Create cart item with variant
	const createCartItemWithVariant = (variant: ProductVariantsType) => {
		if (!selectedProduct) return;

		const cartItem = {
			...selectedProduct,
			price: variant.variantPrice,
			selectedVariant: variant,
			selectedOptions: Object.values(selectedOptions),
		};
		setCartItem(cartItem);
	};

	// Create cart item with options only
	const createCartItemWithOptions = () => {
		if (!selectedProduct) return;

		const cartItem = {
			...selectedProduct,
			selectedOptions: Object.values(selectedOptions),
		};
		setCartItem(cartItem);
	};

	// Handle back navigation
	const handleBack = () => {
		if (currentStep === "variant-selection") {
			setCurrentStep("product-selection");
			setSelectedProduct(null);
			setProductId("");
		} else if (currentStep === "options-selection") {
			if (selectedVariant) {
				setCurrentStep("variant-selection");
			} else {
				setCurrentStep("product-selection");
				setSelectedProduct(null);
				setProductId("");
			}
			setSelectedOptions({});
		}
	};

	// Check if all options are selected
	const areAllOptionsSelected = () => {
		if (!selectedProduct?.productOptions) return true;
		return selectedProduct.productOptions.every(
			(option) =>
				selectedOptions[option.optionName] && selectedOptions[option.optionName].trim() !== ""
		);
	};

	console.log(productId, " productId", currentStep, " currentStep");

	// Render product selection step
	const renderProductSelection = () => (
		<>
			<Input
				ref={inputRef}
				isClearable
				size="sm"
				classNames={{
					base: "w-full rounded-full",
				}}
				radius="sm"
				placeholder="Search..."
				startContent={<BiSearch />}
				value={filterValue}
				onClear={onClear}
				onValueChange={setFilterValue}
			/>
			<div ref={scrollContainerRef} className="max-h-96 h-60 overflow-y-scroll scrollbar w-full">
				{loading && !data ? (
					<Spinner className="mt-10 text-center w-full" />
				) : (
					<RadioGroup className="w-full" value={productId} onValueChange={handleProductSelect}>
						{data.getProducts.products.map((product: GetProductsType, index: number) => (
							<div
								key={product._id}
								ref={index === data.getProducts.products.length - 1 ? observerRef : null}
								className="w-full flex"
							>
								<CustomRadio
									value={product._id}
									endContent={product.price ? `₹${product.price.toFixed(2)}` : ""}
								>
									<div className="flex items-center gap-2">
										{product?.assets[0]?.path ? (
											<Image
												src={product.assets[0]?.path || "/images/placeholder.svg"}
												alt="product"
												className="w-10 h-10 rounded-md"
											/>
										) : (
											<CiImageOn className="text-[#3b82f6] text-4xl m-auto" />
										)}
										<div className="flex flex-col">
											<span> {product.name}</span>
											<span> {`${product.totalProductQuantity} in stock`}</span>
										</div>
									</div>
								</CustomRadio>
							</div>
						))}
					</RadioGroup>
				)}
				{loading && data ? <Spinner className="mt-2 text-center w-full" /> : null}
			</div>
		</>
	);

	// Render variant selection step
	const renderVariantSelection = () => {
		if (!selectedProduct) return null;

		const publishedVariants =
			selectedProduct.variants?.filter((variant) => variant.status === StatusTypes.PUBLISHED) || [];

		return (
			<div className="flex flex-col gap-4">
				{/* Header with back button */}
				<div className="flex items-center gap-2">
					<Button isIconOnly variant="light" size="sm" onPress={handleBack}>
						<BiArrowLeft rlassName="text-lg" />
					</Button>
					<h3 className="text-lg font-semibold">Select Variant</h3>
				</div>

				{/* Product info */}
				<div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
					{selectedProduct?.assets[0]?.path ? (
						<Image
							src={selectedProduct.assets[0]?.path || "/images/placeholder.svg"}
							alt="product"
							className="w-12 h-12 rounded-md"
						/>
					) : (
						<CiImageOn className="text-[#3b82f6] text-4xl" />
					)}
					<div>
						<h4 className="font-medium">{selectedProduct.name}</h4>
						<p className="text-sm text-gray-600">{`${selectedProduct.totalProductQuantity} in stock`}</p>
					</div>
				</div>

				{/* Variants list */}
				<div className="max-h-60 overflow-y-scroll scrollbar">
					<RadioGroup
						className="w-full"
						onValueChange={(value) => {
							const variant = publishedVariants.find((v) => v.sku === value);
							if (variant) handleVariantSelect(variant);
						}}
					>
						{publishedVariants.map((variant, index) => (
							<div key={variant.sku || index} className="w-full flex">
								<CustomRadio
									value={variant.sku || index.toString()}
									endContent={`₹${variant.variantPrice.toFixed(2)}`}
								>
									<div className="flex items-center gap-2">
										{selectedProduct?.assets[0]?.path ? (
											<Image
												src={selectedProduct.assets[0]?.path || "/images/placeholder.svg"}
												alt="variant"
												className="w-10 h-10 rounded-md"
											/>
										) : (
											<CiImageOn className="text-[#3b82f6] text-4xl m-auto" />
										)}
										<div className="flex flex-col">
											<span>{variant.selectedOptions?.join(" | ") || "Default Variant"}</span>
											<span className="text-sm text-gray-600">SKU: {variant.sku}</span>
										</div>
									</div>
								</CustomRadio>
							</div>
						))}
					</RadioGroup>
				</div>
			</div>
		);
	};

	// Render options selection step
	const renderOptionsSelection = () => {
		if (!selectedProduct) return null;

		return (
			<div className="flex flex-col gap-4">
				{/* Header with back button */}
				<div className="flex items-center gap-2">
					<Button isIconOnly variant="light" size="sm" onPress={handleBack}>
						<BiLeftArrow className="text-lg" />
					</Button>
					<h3 className="text-lg font-semibold">Select Options</h3>
				</div>

				{/* Product info */}
				<div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
					{selectedProduct?.assets[0]?.path ? (
						<Image
							src={selectedProduct.assets[0]?.path || "/images/placeholder.svg"}
							alt="product"
							className="w-12 h-12 rounded-md"
						/>
					) : (
						<CiImageOn className="text-[#3b82f6] text-4xl" />
					)}
					<div>
						<h4 className="font-medium">{selectedProduct.name}</h4>
						{selectedVariant && (
							<p className="text-sm text-gray-600">
								{selectedVariant.selectedOptions?.join(" | ")} - ₹
								{selectedVariant.variantPrice.toFixed(2)}
							</p>
						)}
						{!selectedVariant && (
							<p className="text-sm text-gray-600">₹{selectedProduct.price?.toFixed(2)}</p>
						)}
					</div>
				</div>

				{/* Options */}
				<div className="flex flex-col gap-4 max-h-60 overflow-y-scroll scrollbar">
					{selectedProduct.productOptions?.map((option, index) => (
						<div key={index} className="flex flex-col gap-2">
							<label className="text-sm font-medium">{option.optionName}</label>
							<Select
								placeholder={`Select ${option.optionName}`}
								size="sm"
								selectedKeys={
									selectedOptions[option.optionName] ? [selectedOptions[option.optionName]] : []
								}
								onSelectionChange={(keys) => {
									const selectedKey = Array.from(keys)[0] as string;
									if (selectedKey) {
										handleOptionChange(option.optionName, selectedKey);
									}
								}}
								classNames={{
									trigger: "border border-gray-300 rounded-md",
								}}
							>
								{option.choices?.map((choice) => (
									<SelectItem key={choice} value={choice}>
										{choice}
									</SelectItem>
								))}
							</Select>
						</div>
					))}
				</div>

				{/* Add to Order button */}
				<Button
					color="primary"
					isDisabled={!areAllOptionsSelected()}
					onPress={() => {
						if (selectedVariant) {
							createCartItemWithVariant(selectedVariant);
						} else {
							createCartItemWithOptions();
						}
					}}
					className="mt-4"
				>
					Add to Order
				</Button>
			</div>
		);
	};

	// Main render based on current step
	return (
		<div className="w-full">
			{currentStep === "product-selection" && renderProductSelection()}
			{currentStep === "variant-selection" && renderVariantSelection()}
			{currentStep === "options-selection" && renderOptionsSelection()}
		</div>
	);
};

export default SelectProduct;
