// hooks/useSingleMutationStatusHandler.ts

import { addToast } from "@heroui/react";
import { useEffect } from "react";

interface UseMutationStatusHandlerProps {
  data: any;
  loading: boolean;
  error: any;
  successMessage?: string;
  onClose?: () => void; // function to close the drawer or modal when success or error occurs
}

export const useMutationHandler = ({
  data,
  loading,
  error,
  successMessage,
  onClose,
}: UseMutationStatusHandlerProps) => {
  useEffect(() => {
    if (error) {
      const message = error?.message || "Something went wrong";
      addToast({
        title: message,
        color: "danger",
        shouldShowTimeoutProgress: true,
      });
      if (onClose) {
        onClose();
      }
    }

    if (data && !loading) {
      addToast({
        title: successMessage,
        color: "success",
        shouldShowTimeoutProgress: true,
      });
      if (onClose) {
        onClose();
      }
    }
  }, [data, loading, error]);
};
