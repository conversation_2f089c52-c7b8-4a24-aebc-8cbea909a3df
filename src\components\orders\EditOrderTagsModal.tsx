import { useMutation, useQuery } from "@apollo/client";
import {
  <PERSON><PERSON>,
  Di<PERSON>r,
  Input,
  Radio,
  RadioGroup,
  Spinner,
} from "@heroui/react";
import { useRef, useState } from "react";
import {
  CREATE_ORDER_TAG,
  DELETE_ORDER_TAG,
  GET_ORDER_TAGS,
  UPDATE_ORDER_TAG,
} from "../../graphql/orderTags";
import { useBoundStore } from "../../store/store";
import { BiCheck, BiPencil, BiPlus, BiTrash, BiX } from "react-icons/bi";
import { useMutationHandler } from "../../hooks/useMutationStatusHandler";
import { useInfiniteQueryScroll } from "../../hooks/useInfiniteQueryScroll";

interface OrderTagType {
  _id: string;
  name: string;
  isDeleted?: boolean;
  isDefault?: boolean;
  orders?: {
    totalCount: number;
  };
}

const EditOrderTagsModal = () => {
  const [addNewTag, setAddNewTag] = useState(false);
  const [newTagName, setNewTagName] = useState("");
  const [editingTagId, setEditingTagId] = useState<string | null>(null);
  const [editingTagName, setEditingTagName] = useState("");
  const { selectedOrderTagId, setSelectedOrderTagId } = useBoundStore();
  const { data, loading, fetchMore } = useQuery(GET_ORDER_TAGS, {
    variables: { limit: 10, offset: 0, filters: { search: null } },
    notifyOnNetworkStatusChange: true,
  });

  const [
    deleteOrderTag,
    {
      data: deleteOrderTagData,
      loading: deleteOrderTagLoading,
      error: deleteOrderTagError,
    },
  ] = useMutation(DELETE_ORDER_TAG, {
    refetchQueries: [GET_ORDER_TAGS, "GetOrderTagsByFilter"],
  });

  const [
    createOrderTag,
    {
      data: createOrderTagData,
      loading: createOrderTagLoading,
      error: createOrderTagError,
    },
  ] = useMutation(CREATE_ORDER_TAG, {
    refetchQueries: [GET_ORDER_TAGS, "GetOrderTagsByFilter"],
  });

  const [
    updateOrderTag,
    {
      data: updateOrderTagData,
      loading: updateOrderTagLoading,
      error: updateOrderTagError,
    },
  ] = useMutation(UPDATE_ORDER_TAG, {
    refetchQueries: [GET_ORDER_TAGS, "GetOrderTagsByFilter"],
  });

  useMutationHandler({
    data: deleteOrderTagData,
    loading: deleteOrderTagLoading,
    error: deleteOrderTagError,
    successMessage: "Order tag deleted successfully",
  });

  useMutationHandler({
    data: createOrderTagData,
    loading: createOrderTagLoading,
    error: createOrderTagError,
    successMessage: "Order tag created successfully",
  });

  useMutationHandler({
    data: updateOrderTagData,
    loading: updateOrderTagLoading,
    error: updateOrderTagError,
    successMessage: "Order tag updated successfully",
  });

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const { observerRef, hasMore } = useInfiniteQueryScroll({
    items: data?.getOrderTagsByFilter?.data || [],
    totalCount: data?.getOrderTagsByFilter?.totalCount || 0,
    loading,
    fetchMore,
    scrollContainerRef,
  });

  const handleNewTag = () => {
    if (newTagName.trim()) {
      createOrderTag({ variables: { name: newTagName } });
      setNewTagName("");
      setAddNewTag(false);
    }
  };

  // Handle edit tag
  const handleEditTag = (tagId: string, tagName: string) => {
    setEditingTagId(tagId);
    setEditingTagName(tagName);
  };

  // Handle save edited tag
  const handleSaveEditedTag = () => {
    if (editingTagId && editingTagName.trim()) {
      updateOrderTag({
        variables: {
          updateOrderTagId: editingTagId,
          input: {
            name: editingTagName,
          },
        },
      });
      setEditingTagId(null);
      setEditingTagName("");
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingTagId(null);
    setEditingTagName("");
  };

  if (loading && !data) return <p>Loading...</p>;

  return (
    <div className="flex flex-col gap-y-2">
      <p>
        Select a tag to assign to the selected orders. If these orders already
        have tags, the tag you select here will be added to them.
      </p>
      <Divider />
      <RadioGroup
        value={selectedOrderTagId}
        onValueChange={setSelectedOrderTagId}
        classNames={{ wrapper: "flex flex-col gap-y-2" }}
        className="max-h-60 overflow-y-scroll scrollbar"
        ref={scrollContainerRef}
      >
        {data?.getOrderTagsByFilter?.data?.map(
          (tag: OrderTagType, index: number) => (
            <div
              key={tag._id}
              className="group flex w-full justify-between"
              ref={
                index === data.getOrderTagsByFilter.data.length - 1 && hasMore
                  ? observerRef
                  : null
              }
            >
              {editingTagId === tag._id ? (
                <div className="flex items-center gap-x-2 w-full">
                  <Input
                    classNames={{
                      inputWrapper: "rounded-md",
                      label: "text-sm",
                    }}
                    value={editingTagName}
                    onValueChange={setEditingTagName}
                    placeholder="Enter tag name"
                    size="sm"
                    className="flex-grow"
                  />
                  <div className="flex gap-x-2">
                    <Button
                      isIconOnly
                      startContent={<BiX className="text-2xl" />}
                      onPress={handleCancelEdit}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                    <Button
                      isIconOnly
                      startContent={<BiCheck className="text-2xl" />}
                      onPress={handleSaveEditedTag}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                  </div>
                </div>
              ) : (
                <>
                  <Radio value={tag._id}>
                    <div className="text-small">
                      {tag.name}{" "}
                      <small>
                        {tag.orders
                          ? `(${tag.orders.totalCount} Orders)`
                          : "(0 Orders)"}
                      </small>
                    </div>
                  </Radio>
                  <div className="group-hover:flex opacity-0 group-hover:opacity-100 gap-x-1">
                    <Button
                      isIconOnly
                      startContent={<BiPencil />}
                      onPress={() => handleEditTag(tag._id, tag.name)}
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                    <Button
                      isIconOnly
                      startContent={<BiTrash />}
                      onPress={() =>
                        deleteOrderTag({
                          variables: { deleteOrderTagId: tag._id },
                        })
                      }
                      className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
                      size="sm"
                      variant="ghost"
                      radius="full"
                    />
                  </div>
                </>
              )}
            </div>
          )
        )}
        {loading && <Spinner className="mt-4" />}
      </RadioGroup>

      {addNewTag ? (
        <div className="flex items-center gap-x-2">
          <Input
            classNames={{
              inputWrapper: "rounded-md",
              label: "text-sm",
            }}
            value={newTagName}
            onValueChange={setNewTagName}
            placeholder="Enter new tag name"
            size="sm"
          />
          <div className="flex gap-x-2">
            <Button
              isIconOnly
              startContent={<BiX className="text-2xl" />}
              onPress={() => {
                setAddNewTag(false);
                setNewTagName("");
              }}
              className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
              size="sm"
              variant="ghost"
              radius="full"
            />
            <Button
              isIconOnly
              startContent={<BiCheck className="text-2xl" />}
              onPress={handleNewTag}
              className="bg-lightPrimary dark:bg-slate-700 text-primary border-none"
              size="sm"
              variant="ghost"
              radius="full"
            />
          </div>
        </div>
      ) : (
        <button
          className="text-primary text-small flex items-center gap-x-2"
          onClick={() => setAddNewTag(true)}
        >
          <BiPlus className="text-lg" />
          <span>Add New Tag</span>
        </button>
      )}
    </div>
  );
};

export default EditOrderTagsModal;
