import { Image } from "@heroui/react";

const VisibilityChangeModalContent = () => {
  return (
    <div className="flex items-center gap-x-4">
      <Image
        src="/images/change-visibility.avif"
        width={200}
        height={120}
        alt="change-visibility"
      />
      <div className="flex flex-col gap-y-2">
        <p>
          This will change the visibilty of all the variants of the selected
          products.
        </p>
        <div className="border border-primary rounded bg-lightPrimary p-2">
          <h5 className="font-semibold">
            Do you want to hide particular variant insted of all ?
          </h5>
          <p className="text-sm">
            You can change a particular variant visibility in the inventory tab.
          </p>
        </div>
      </div>
    </div>
  );
};

export default VisibilityChangeModalContent;
