import { stockStatus } from "./../../helpers/constants";
import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import { ProductType, StatusTypes, StockStatus } from "../../types/commonTypes";
import {
  GetInventoryType,
  InventorySetTypeEnum,
  InventoryUpdateType,
} from "../../types/inventoryType";

const INITIAL_VISIBLE_COLUMNS = ["name", "price", "stockStatus", "isDeleted"];

export interface InventorySlice {
  visibleColumns: Set<string>;
  hasMore: boolean;
  selectedKeys: Set<string> | "all";
  inventoryFilterText: string;
  inventory: GetInventoryType[];
  inventoryFilters: {
    status: StatusTypes | null;
    stockStatus: StockStatus | null;
    productType: ProductType | null;
  };
  setVisibleColumns: (value: Set<string>) => void;
  setHasMore: (value: boolean) => void;
  setSelectedKeys: (value: Set<string> | "all") => void;
  setInventoryFilterText: (value: string) => void;
  setInventory: (value: GetInventoryType[]) => void;
  setInventoryFilters: (value: {
    status?: StatusTypes | null;
    stockStatus?: StockStatus | null;
    productType?: ProductType | null;
  }) => void;
  bulkInventoryUpdate: {
    trackInventory: boolean | null;
    setQuantity: {
      setType: InventorySetTypeEnum | null;
      value: number | null;
    };
    showAllItemAs: StockStatus | null;
  };
  setBulkInventoryUpdate: (
    value: Partial<InventorySlice["bulkInventoryUpdate"]>
  ) => void;
  resetBulkInventoryUpdate: () => void;
}

const initialState: Omit<
  InventorySlice,
  | "setVisibleColumns"
  | "setHasMore"
  | "setSelectedKeys"
  | "setInventoryFilterText"
  | "setInventory"
  | "setInventoryFilters"
  | "setBulkInventoryUpdate"
  | "resetBulkInventoryUpdate"
> = {
  visibleColumns: new Set(INITIAL_VISIBLE_COLUMNS),
  hasMore: false,
  selectedKeys: new Set([]),
  inventoryFilterText: "",
  inventory: [],
  inventoryFilters: {
    status: null,
    stockStatus: null,
    productType: null,
  },
  bulkInventoryUpdate: {
    trackInventory: null,
    showAllItemAs: null,
    setQuantity: {
      setType: null,
      value: null,
    },
  },
};

export const inventorySlice: StateCreator<
  InventorySlice,
  [],
  [["zustand/immer", never]]
> = immer((set) => ({
  ...initialState,

  setVisibleColumns: (value: Set<string>) =>
    set((state) => {
      state.visibleColumns = value;
    }),
  setHasMore: (value) =>
    set((state) => {
      state.hasMore = value;
    }),
  setSelectedKeys: (value) =>
    set((state) => {
      state.selectedKeys = value;
    }),

  setInventoryFilterText: (value) =>
    set((state) => {
      state.inventoryFilterText = value;
    }),
  setInventory: (value: GetInventoryType[]) =>
    set((state) => {
      state.inventory = value;
    }),
  setInventoryFilters: (value: Partial<typeof initialState.inventoryFilters>) =>
    set((state) => {
      state.inventoryFilters = {
        ...state.inventoryFilters,
        ...value,
      };
    }),
  setBulkInventoryUpdate: (
    value: Partial<typeof initialState.bulkInventoryUpdate>
  ) =>
    set((state) => {
      state.bulkInventoryUpdate = {
        ...state.bulkInventoryUpdate,
        ...value,
      };
    }),
  resetBulkInventoryUpdate: () =>
    set((state) => {
      state.bulkInventoryUpdate = initialState.bulkInventoryUpdate;
    }),
}));
