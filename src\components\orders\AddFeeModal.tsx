import { Input, NumberInput } from "@heroui/react";
import { Control, Controller, UseFormSetValue, useFieldArray, UseFormWatch } from "react-hook-form";
import { OrderSchema } from "../../types/orderType";
import { BiP<PERSON>, BiTrash } from "react-icons/bi";

type AddModalFeeProps = {
	errors: any;
	control: Control<OrderSchema, any>;
	setValue: UseFormSetValue<OrderSchema>;
	watch: UseFormWatch<OrderSchema>;
};

const AddFeeModal = ({ errors, control, setValue, watch }: AddModalFeeProps) => {
	// Use fieldArray to handle the dynamic array of fee options
	const { fields, append, remove } = useFieldArray({
		control,
		name: "customFee",
	});

	// Add a new fee option
	const handleAddFeeOption = () => {
		append({ name: "", amount: 0 });
	};

	return (
		<div className="flex flex-col  gap-4 w-full">
			{fields.map((field, index) => (
				<div key={field.id} className="grid grid-cols-12 w-full place-items-center">
					<div className="grid w-full grid-cols-2 col-span-10 gap-4">
						<Controller
							control={control}
							name={`customFee.${index}.name`}
							render={({ field }) => (
								<Input
									{...field}
									isRequired
									label="Name"
									labelPlacement="outside"
									size="sm"
									classNames={{
										inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md z-10",
										label: "text-base",
									}}
									placeholder="e.g. Gift wrapping"
									isInvalid={!!errors.customFee?.[index]?.name}
									errorMessage={errors.customFee?.[index]?.name?.message}
								/>
							)}
						/>

						{/* Amount */}
						<Controller
							control={control}
							name={`customFee.${index}.amount`}
							render={({ field: { onChange, onBlur, value, ref } }) => (
								<NumberInput
									hideStepper
									label="Amount"
									size="sm"
									ref={ref}
									onValueChange={(value) => {
										onChange(value); // Update form state
										setValue(`customFee.${index}.amount`, value); // Manually update the form value if needed
									}}
									onBlur={onBlur} // Ensure validation runs on blur
									value={value}
									labelPlacement="outside"
									startContent="₹"
									errorMessage={errors.customFee?.[index]?.amount?.message}
									isInvalid={!!errors.customFee?.[index]?.amount?.message}
									classNames={{
										inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md",
										label: "text-base",
									}}
								/>
							)}
						/>
					</div>
					<div className="flex justify-end items-center w-full">
						<button
							type="button"
							className="text-red-500 hover:cursor-pointer"
							onClick={() => remove(index)}
							disabled={fields.length === 0}
						>
							<BiTrash className="text-2xl mt-4" />
						</button>
					</div>
				</div>
			))}

			<button
				type="button"
				className="text-primary w-fit flex items-center"
				onClick={handleAddFeeOption}
			>
				<BiPlus />
				<span> {watch("customFee")?.length > 0 ? "Add Another" : "Add"} Fee</span>
			</button>
		</div>
	);
};

export default AddFeeModal;
