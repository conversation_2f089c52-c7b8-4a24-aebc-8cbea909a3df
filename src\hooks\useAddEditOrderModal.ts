import { useDisclosure } from "@heroui/react";

/**
 * Custom hook to manage all modals in the AddEditOrders page
 */
export const useAddEditOrderModal = () => {
  // Product selection modal
  const {
    isOpen: isProductSelectOpen,
    onOpen: onProductSelectOpen,
    onClose: onProductSelectClose,
    onOpenChange: onProductSelectChange,
  } = useDisclosure();

  // Delivery method modal
  const {
    isOpen: isDeliveryMethodOpen,
    onOpen: onDeliveryMethodOpen,
    onClose: onDeliveryMethodClose,
    onOpenChange: onDeliveryMethodChange,
  } = useDisclosure();

  // Fee modal
  const {
    isOpen: isFeeOpen,
    onOpen: onFeeOpen,
    onClose: onFeeClose,
    onOpenChange: onFeeChange,
  } = useDisclosure();

  // Discount modal
  const {
    isOpen: isDiscountOpen,
    onOpen: onDiscountOpen,
    onClose: onDiscountClose,
    onOpenChange: onDiscountChange,
  } = useDisclosure();

  // Location modal
  const {
    isOpen: isLocationOpen,
    onOpen: onLocationOpen,
    onClose: onLocationClose,
    onOpenChange: onLocationChange,
  } = useDisclosure();

  return {
    // Product selection modal
    productSelectModal: {
      isOpen: isProductSelectOpen,
      onOpen: onProductSelectOpen,
      onClose: onProductSelectClose,
      onOpenChange: onProductSelectChange,
    },
    
    // Delivery method modal
    deliveryMethodModal: {
      isOpen: isDeliveryMethodOpen,
      onOpen: onDeliveryMethodOpen,
      onClose: onDeliveryMethodClose,
      onOpenChange: onDeliveryMethodChange,
    },
    
    // Fee modal
    feeModal: {
      isOpen: isFeeOpen,
      onOpen: onFeeOpen,
      onClose: onFeeClose,
      onOpenChange: onFeeChange,
    },
    
    // Discount modal
    discountModal: {
      isOpen: isDiscountOpen,
      onOpen: onDiscountOpen,
      onClose: onDiscountClose,
      onOpenChange: onDiscountChange,
    },

    // Location modal
    locationModal: {
      isOpen: isLocationOpen,
      onOpen: onLocationOpen,
      onClose: onLocationClose,
      onOpenChange: onLocationChange,
    },
  };
};
