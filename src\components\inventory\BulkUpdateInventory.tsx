import { NumberInput, Select, SelectItem, Tab, Tabs } from "@heroui/react";
import { ChangeEvent, useMemo } from "react";
import { useBoundStore } from "../../store/store";
import {
  GetInventoryType,
  InventorySetTypeEnum,
} from "../../types/inventoryType";
import { formatStockStatus } from "../../helpers/formatters";
import { StockStatus } from "../../types/commonTypes";
import { IoRepeat } from "react-icons/io5";
import { BiPlus } from "react-icons/bi";

type BulkUpdateInventoryProps = {
  finalSelectedKeys: string[];
};
const BulkUpdateInventory = ({
  finalSelectedKeys,
}: BulkUpdateInventoryProps) => {
  const { inventory, bulkInventoryUpdate, setBulkInventoryUpdate } =
    useBoundStore();

  const finalSelectedProducts = useMemo(() => {
    const filteredInventory = inventory.filter((item: GetInventoryType) =>
      finalSelectedKeys.includes(item._id)
    );

    if (
      bulkInventoryUpdate.trackInventory === false &&
      bulkInventoryUpdate.showAllItemAs === "OUT_OF_STOCK"
    ) {
      return filteredInventory.map((item: GetInventoryType) => ({
        ...item,
        trackInventory: false,
        stockStatus:
          bulkInventoryUpdate.showAllItemAs || StockStatus.OUT_OF_STOCK,
      }));
    }
    if (
      bulkInventoryUpdate.trackInventory === false &&
      bulkInventoryUpdate.showAllItemAs === "IN_STOCK"
    ) {
      return filteredInventory.map((item: GetInventoryType) => ({
        ...item,
        trackInventory: false,
        stockStatus: StockStatus.IN_STOCK,
      }));
    }

    if (
      bulkInventoryUpdate.trackInventory === true &&
      bulkInventoryUpdate.setQuantity?.setType === InventorySetTypeEnum.SET
    ) {
      return filteredInventory.map((item: GetInventoryType) => ({
        ...item,
        trackInventory: true,
        stockQuantity: bulkInventoryUpdate.setQuantity?.value ?? 0,
      }));
    }
    if (
      bulkInventoryUpdate.trackInventory === true &&
      bulkInventoryUpdate.setQuantity?.setType === InventorySetTypeEnum.ADD
    ) {
      return filteredInventory.map((item: GetInventoryType) => ({
        ...item,
        trackInventory: true,
        stockQuantity:
          item.stockQuantity + (bulkInventoryUpdate.setQuantity?.value ?? 0),
      }));
    }

    return filteredInventory;
  }, [
    inventory,
    finalSelectedKeys,
    bulkInventoryUpdate.trackInventory,
    bulkInventoryUpdate.showAllItemAs,
    bulkInventoryUpdate.setQuantity?.value,
  ]);

  const handleQuantityChange = (e: ChangeEvent<HTMLInputElement> | number) => {
    setBulkInventoryUpdate({
      ...bulkInventoryUpdate,
      setQuantity: {
        ...bulkInventoryUpdate.setQuantity,
        setType: bulkInventoryUpdate.setQuantity?.setType ?? null,
        value: typeof e === "number" ? e : parseInt(e.target.value),
      },
    });
  };
  return (
    <div>
      <div className="flex flex-col gap-5">
        <Select
          label="Inventory Tracking"
          isRequired
          size="sm"
          className="max-w-xs"
          classNames={{
            trigger: "rounded-md",
          }}
          labelPlacement="outside"
          placeholder="Select"
          onSelectionChange={(value) => {
            console.log(value.currentKey, " vhoef");
            setBulkInventoryUpdate({
              ...bulkInventoryUpdate,
              trackInventory: value.currentKey === "track",
            });
          }}
          value={bulkInventoryUpdate.trackInventory ? "track" : "doNotTrack"}
        >
          <SelectItem key={"doNotTrack"}>Don't Track by Quantity</SelectItem>
          <SelectItem key={"track"}>Track by Quantity</SelectItem>
        </Select>
        {!bulkInventoryUpdate.trackInventory &&
        bulkInventoryUpdate.trackInventory !== null ? (
          <Select
            label="Show all item as:"
            size="sm"
            isRequired
            className="max-w-xs mt-5"
            classNames={{
              trigger: "rounded-md",
            }}
            labelPlacement="outside"
            placeholder="Select"
            value={bulkInventoryUpdate.showAllItemAs ?? ""}
            onSelectionChange={(value) => {
              setBulkInventoryUpdate({
                ...bulkInventoryUpdate,
                showAllItemAs: value.currentKey as StockStatus,
              });
            }}
          >
            <SelectItem key={StockStatus.IN_STOCK}>In Stock</SelectItem>
            <SelectItem key={StockStatus.OUT_OF_STOCK}>Out of Stock</SelectItem>
          </Select>
        ) : null}
        {bulkInventoryUpdate.trackInventory &&
        bulkInventoryUpdate.trackInventory !== null ? (
          <div className="flex items-center gap-x-5">
            <Tabs
              classNames={{
                tabList: "rounded-md",
              }}
              aria-label="Sale Type"
              size="sm"
              selectedKey={bulkInventoryUpdate.setQuantity?.setType ?? ""}
              onSelectionChange={(value) => {
                setBulkInventoryUpdate({
                  ...bulkInventoryUpdate,
                  setQuantity: {
                    ...bulkInventoryUpdate.setQuantity,
                    setType: value as InventorySetTypeEnum,
                  },
                });
              }}
            >
              <Tab
                key={InventorySetTypeEnum.ADD}
                title={
                  <div className="flex gap-x-2 items-center">
                    <BiPlus /> <span>Add</span>
                  </div>
                }
                aria-label="Add"
              />
              <Tab
                key={InventorySetTypeEnum.SET}
                title={
                  <div className="flex gap-x-2 items-center">
                    <IoRepeat /> <span>SET</span>
                  </div>
                }
                aria-label="Set"
              />
            </Tabs>

            <div>
              <NumberInput
                size="sm"
                aria-label="Quantity"
                onChange={handleQuantityChange}
                placeholder="0"
                classNames={{
                  inputWrapper:
                    "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0 h-9",
                }}
                value={bulkInventoryUpdate.setQuantity?.value ?? undefined}
              />
            </div>
          </div>
        ) : null}
      </div>
      <div className="mt-5">
        <p>You are updating {finalSelectedKeys.length} items</p>
        <div className="border rounded-md mt-2">
          {finalSelectedProducts.map((item: GetInventoryType) => (
            <div
              key={item._id}
              className="h-16 w-full border-b flex justify-between px-5 items-center text-sm"
            >
              <div>{item.name}</div>
              <div>
                {item.trackInventory
                  ? `Quantity: ${item.stockQuantity ?? 0}`
                  : formatStockStatus(item.stockStatus)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BulkUpdateInventory;
