export enum ChangePriceType {
  INCREASE_BY_AMOUNT = "INCREASE_BY_AMOUNT",
  REDUCE_BY_AMOUNT = "REDUCE_BY_AMOUNT",
  SET_A_NEW_PRICE = "SET_A_NEW_PRICE",
  INCREASE_BY_PERCENTAGE = "INCREASE_BY_PERCENTAGE",
  REDUCE_BY_PERCENTAGE = "REDUCE_BY_PERCENTAGE",
}

export enum StatusTypes {
  PUBLISHED = "PUBLISHED",
  SCHEDULED = "SCHEDULED",
  ARCHIVED = "ARCHIVED",
  DRAFT = "DRAFT",
}

export enum StockStatus {
  IN_STOCK = "IN_STOCK",
  OUT_OF_STOCK = "OUT_OF_STOCK",
}
export enum ProductType {
  PHYSICAL = "PHYSICAL",
  DIGITAL = "DIGITAL",
}