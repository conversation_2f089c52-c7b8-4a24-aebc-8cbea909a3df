import { Chip } from "@heroui/react";
import { PaymentStatus } from "../../types/orderType";
type PaymentDetailProps = {
	paymentStatus: PaymentStatus;
	totalAmount: number;
	itemsAmount: number;
	shippingAmount: number;
	couponAmount: number;
	taxAmount: number;
	couponName: string;
	customFee: {
		amount: number;
		name: string;
	}[];
	customDiscount: {
		amount: number;
		discountReason: string;
	}[];
};
const PaymentDetail = ({
	paymentStatus,
	totalAmount,
	itemsAmount,
	shippingAmount,
	couponAmount,
	taxAmount,
	couponName,
	customFee,
	customDiscount,
}: PaymentDetailProps) => {
	return (
		<div className="flex flex-col gap-y-2">
			<div className="flex justify-between w-full gap-4">
				<p>Payment Status</p>
				<p>
					<Chip
						className="capitalize"
						color={paymentStatus === PaymentStatus.PAID ? "success" : "danger"}
						size="sm"
						variant="flat"
					>
						{paymentStatus}
					</Chip>
				</p>
			</div>
			<div className="flex justify-between w-full gap-4">
				<p>Items</p>
				{itemsAmount ? <p>₹{itemsAmount}</p> : <p>-</p>}
			</div>
			<div className="flex justify-between w-full gap-4">
				<p>Shipping</p>
				{shippingAmount ? <p>₹{shippingAmount}</p> : <p>-</p>}
			</div>
			{couponName && couponAmount ? (
				<div className="flex justify-between w-full gap-4">
					<p>{couponName}</p>
					<p>₹{couponAmount}</p>
				</div>
			) : null}
			<div className="flex justify-between w-full gap-4">
				<p>Tax</p>
				{taxAmount ? <p>₹{taxAmount}</p> : <p>-</p>}
			</div>
			{customFee.length > 0
				? customFee.map((fee) => (
						<div className="flex justify-between w-full gap-4">
							<p>{fee.name}</p>
							<p>₹{fee.amount}</p>
						</div>
				  ))
				: null}
			{customDiscount.length > 0
				? customDiscount.map((discount) => (
						<div className="flex justify-between w-full gap-4">
							<p>{discount.discountReason}</p>
							<p>-₹{discount.amount}</p>
						</div>
				  ))
				: null}
			<div className="flex justify-between  w-full gap-4 border-t py-2 ">
				<p className="font-semibold">Total</p>
				{totalAmount ? <p className="font-semibold">₹{totalAmount}</p> : <p>-</p>}
			</div>
		</div>
	);
};

export default PaymentDetail;
