import { Switch, cn } from "@heroui/react";

type CustomSwitchProps = {
  label: string;
  selected: boolean;
  onSelectionChange: () => void;
};
export default function CustomSwitch({
  label = "",
  selected = false,
  onSelectionChange = () => {},
}: CustomSwitchProps) {
  return (
    <Switch
      isSelected={selected}
      onValueChange={onSelectionChange}
      classNames={{
        base: cn(
          "inline-flex flex-row-reverse w-fit bg-content1 items-center",
          "justify-between cursor-pointer rounded-lg gap-2 border-2 border-transparent"
        ),
        wrapper: "p-0 h-4 flex  overflow-visible",
        thumb: cn(
          "w-6 h-6 border-2 shadow-lg",
          //selected
          "group-data-[selected=true]:ms-6",
          // pressed
          "group-data-[pressed=true]:w-7",
          "group-data-[selected]:group-data-[pressed]:ms-4"
        ),
      }}
    >
      <div className="flex flex-col gap-1">
        <p className="text-medium">{label}</p>
      </div>
    </Switch>
  );
}
