import { Outlet, useNavigate } from "react-router";
import Sidebar from "../components/Sidebar";
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  Input,
  DropdownItem,
  DropdownTrigger,
  Dropdown,
  DropdownMenu,
  Button,
  addToast,
} from "@heroui/react";
import { BiMessageDetail, BiSearch, BiX, BiMenu } from "react-icons/bi";
import ThemeSwitcher from "../components/ThemeSwitcher";
import { IoIosNotifications } from "react-icons/io";
import { useBoundStore } from "../store/store";
import ProtectedRoute from "../components/ProtectedRoute";
import { useState } from "react";

const SideBarLayout = () => {
  const navigate = useNavigate();
  const user = useBoundStore((state) => state.user);
  const logout = useBoundStore((state) => state.logOut);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  return (
    <>
      <Navbar
        isBordered={false}
        height={50}
        className="w-full"
        classNames={{
          wrapper:
            "max-w-full bg-textStandardPrimary dark:bg-slate-900 dark:text-white sticky top-0 left-0",
          content: "flex justify-between text-white dark:text-white",
        }}
      >
        <NavbarContent justify="start" className="gap-2">
          <Button
            isIconOnly
            aria-label="Menu"
            variant="light"
            className="lg:hidden text-white"
            onPress={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
          >
            <BiMenu size={24} />
          </Button>
          <NavbarBrand className="mr-4">
            <p className="text-xl font-bold text-inherit">ZCOMMERCE</p>
          </NavbarBrand>
        </NavbarContent>

        <NavbarContent className="items-center hidden lg:flex">
          <Input
            classNames={{
              base: "max-w-full",
              mainWrapper: "h-8 mx-auto",
              input: "text-small",
              inputWrapper:
                "min-h-7 bg-grayBackground w-96 rounded-full font-normal text-textPlaceHolder dark:text-default-500 dark:bg-default-400/20 dark:bg-default-500/20",
            }}
            placeholder="Type to search..."
            size="sm"
            startContent={<BiSearch size={18} />}
            type="search"
            aria-label="search"
          />
        </NavbarContent>

        <NavbarContent
          as="div"
          className="items-center gap-2 sm:gap-5"
          justify="end"
        >
          <div className="relative hidden sm:block">
            <Button
              isIconOnly
              aria-label="Notifications"
              size="sm"
              className="bg-grayBackground rounded text-white"
            >
              <IoIosNotifications
                className="fill-current text-tiny"
                size={20}
              />
            </Button>
            <span className="absolute text-tiny -top-2 -right-2 p-1 h-5 w-5 bg-primary rounded-full flex items-center justify-center">
              5
            </span>
          </div>
          <div className="relative hidden sm:block">
            <Button
              isIconOnly
              aria-label="Messages"
              size="sm"
              className="bg-grayBackground rounded text-white"
            >
              <BiMessageDetail className="fill-current text-tiny" size={20} />
            </Button>
            <span className="absolute text-tiny -top-2 -right-2 p-1 h-5 w-5 bg-primary rounded-full flex items-center justify-center">
              5
            </span>
          </div>

          <ThemeSwitcher />
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <div className="flex items-center hover:cursor-pointer gap-2 p-1 bg-grayBackground hover:bg-opacity-hover rounded pl-2 text-small">
                <h5 className="hidden sm:block">{user?.clientName} </h5>
                <div className="font-medium bg-primary p-1 rounded">
                  {user?.clientName?.slice(0, 2).toUpperCase()}
                </div>
              </div>
            </DropdownTrigger>
            <DropdownMenu aria-label="Profile Actions" variant="flat">
              <DropdownItem
                key="profile"
                className="h-14 gap-2 hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
                color="primary"
              >
                <p className="font-semibold">Signed in as</p>
                <p className="font-semibold">{user?.email}</p>
              </DropdownItem>
              <DropdownItem
                color="primary"
                key="settings"
                className="hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
              >
                My Settings
              </DropdownItem>
              <DropdownItem
                key="team_settings"
                color="primary"
                className="hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
              >
                Team Settings
              </DropdownItem>
              <DropdownItem
                key="analytics"
                color="primary"
                className="hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
              >
                Analytics
              </DropdownItem>
              <DropdownItem
                key="system"
                color="primary"
                className="hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
              >
                System
              </DropdownItem>
              <DropdownItem
                key="configurations"
                color="primary"
                className="hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
              >
                Configurations
              </DropdownItem>
              <DropdownItem
                key="help_and_feedback"
                color="primary"
                className="hover:bg-lightPrimary hover:dark:bg-slate-700"
                classNames={{
                  base: "data-[hover=true]:bg-lightPrimary data-[hover=true]:dark:bg-slate-700 data-[selectable=true]:focus:bg-lightPrimary data-[selectable=true]:focus:dark:bg-slate-700",
                  wrapper: "hover:bg-lightPrimary hover:dark:bg-slate-700",
                }}
              >
                Help & Feedback
              </DropdownItem>
              <DropdownItem
                key="logout"
                color="danger"
                className="hover:bg-red-100 hover:dark:bg-red-900"
                classNames={{
                  base: "data-[hover=true]:bg-red-100 data-[hover=true]:dark:bg-red-900 data-[selectable=true]:focus:bg-red-100 data-[selectable=true]:focus:dark:bg-red-900",
                  wrapper: "hover:bg-red-100 hover:dark:bg-red-900",
                }}
                onPress={() => {
                  logout();
                  navigate("/sign-in");
                  addToast({
                    title: "You have logged out!",
                    color: "warning",
                  });
                }}
              >
                Log Out
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </NavbarContent>
      </Navbar>
      <div className="flex w-full relative">
        <div
          className={`fixed lg:sticky top-[50px] left-0 h-[calc(100vh-50px)] z-[50] transition-transform duration-300 ${
            isMobileSidebarOpen
              ? "translate-x-0"
              : "-translate-x-full lg:translate-x-0"
          }`}
        >
          <Sidebar onLinkClick={() => setIsMobileSidebarOpen(false)} />
        </div>
        {/* Overlay for mobile sidebar */}
        {isMobileSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-[40] lg:hidden"
            onClick={() => setIsMobileSidebarOpen(false)}
          />
        )}
        <div className="w-full dark:bg-slate-900 pt-8 px-5">
          <ProtectedRoute>
            <Outlet />
          </ProtectedRoute>
        </div>
      </div>
    </>
  );
};

export default SideBarLayout;
