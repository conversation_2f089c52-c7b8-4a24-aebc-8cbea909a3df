import { PaymentStatus, PaymentType } from "../types/paymentTypes";

export const dummyPayments: PaymentType[] = [
  {
    _id: "1",
    date: "4 Apr, 02:00 pm",
    customer: {
      _id: "c1",
      name: "<PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "9876543210"
    },
    product: {
      _id: "p1",
      name: "Gojo Mug Colour:Black, Gojo...",
      price: 673.00,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.SUCCESSFUL,
    amount: 673.00
  },
  {
    _id: "2",
    date: "31 Mar, 12:17 pm",
    customer: {
      _id: "c2",
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "9876543211"
    },
    product: {
      _id: "p2",
      name: "Beserk Mousepad",
      price: 254.10,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.SUCCESSFUL,
    amount: 254.10
  },
  {
    _id: "3",
    date: "30 Mar, 08:53 pm",
    customer: {
      _id: "c3",
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      email: "abhis<PERSON><EMAIL>",
      phone: "9876543212"
    },
    product: {
      _id: "p3",
      name: "Naruto FlipTop Sipper",
      price: 574.00,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.SUCCESSFUL,
    amount: 574.00
  },
  {
    _id: "4",
    date: "29 Mar, 11:26 pm",
    customer: {
      _id: "c4",
      name: "Vansh Malik",
      email: "<EMAIL>",
      phone: "9876543213"
    },
    product: {
      _id: "p4",
      name: "Giyu Desk-Mat",
      price: 809.10,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.SUCCESSFUL,
    amount: 809.10
  },
  {
    _id: "5",
    date: "18 Mar, 12:09 am",
    customer: {
      _id: "c5",
      name: "Siddhi Saraswat",
      email: "<EMAIL>",
      phone: "9876543214"
    },
    product: {
      _id: "p5",
      name: "Haikyu Straw Sipper",
      price: 394.20,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.SUCCESSFUL,
    amount: 394.20
  },
  {
    _id: "6",
    date: "17 Mar, 08:43 pm",
    customer: {
      _id: "c6",
      name: "Tanmay Kuppast",
      email: "<EMAIL>",
      phone: "9876543215"
    },
    product: {
      _id: "p6",
      name: "DBZ Tee Colour:Black | Size:...",
      price: 1043.10,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.SUCCESSFUL,
    amount: 1043.10
  },
  {
    _id: "7",
    date: "17 Mar, 08:27 pm",
    customer: {
      _id: "c7",
      name: "Tanmay Kuppast",
      email: "<EMAIL>",
      phone: "9876543215"
    },
    product: {
      _id: "p7",
      name: "DBZ Tee Colour:Black | Size:...",
      price: 1043.10,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.DECLINED,
    amount: 1043.10
  },
  {
    _id: "8",
    date: "18 Feb, 10:42 am",
    customer: {
      _id: "c8",
      name: "Lalit Patel",
      email: "<EMAIL>",
      phone: "9876543216"
    },
    product: {
      _id: "p8",
      name: "Goku Mug Colour:White",
      price: 249.00,
      quantity: 1
    },
    paymentMethod: "PayU India",
    status: PaymentStatus.DECLINED,
    amount: 249.00
  }
];
