import { gql } from "@apollo/client";

export const GET_ORDER_TAGS = gql`
  query GetOrderTagsByFilter($limit: Int, $offset: Int, $filters: GetOrderTagsFiltersInput) {
    getOrderTagsByFilter(limit: $limit, offset: $offset, filters: $filters) {
      data {
        _id
        name
        isDeleted
      }
      totalCount
    }
  }
`;

export const GET_ORDER_TAG_BY_ID = gql`
  query GetOrderTagById($getOrderTagByIdId: ID!) {
    getOrderTagById(id: $getOrderTagByIdId) {
      _id
      name
      isDefault
    }
  }
`;

export const CREATE_ORDER_TAG = gql`
  mutation CreateOrderTag($name: String!) {
    createOrderTag(name: $name) {
      _id
      name
    }
  }
`;

export const DELETE_ORDER_TAG = gql`
  mutation DeleteOrderTag($deleteOrderTagId: ID!) {
    deleteOrderTag(id: $deleteOrderTagId)
  }
`;

export const UPDATE_ORDER_TAG = gql`
  mutation UpdateOrderTag($updateOrderTagId: ID!, $input: UpdateOrderTagsInput) {
    updateOrderTag(id: $updateOrderTagId, input: $input) {
      _id
      name
      isDeleted
    }
  }
`;
