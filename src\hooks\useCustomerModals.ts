import { useDisclosure } from "@heroui/react";

/**
 * Custom hook to manage all modals in the Customers page
 */
export const useCustomerModals = () => {
  // Main filter modal
  const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure({
    id: "customer-modal-1",
  });

  // Delete confirmation modal for single customer
  const {
    isOpen: isConfirmationOpen,
    onOpen: onConfirmationOpen,
    onClose: onConfirmationClose,
    onOpenChange: onConfirmationOpenChange,
  } = useDisclosure({ id: "customer-modal-2" });

  // Bulk delete customers modal
  const {
    isOpen: isBulkDeleteOpen,
    onOpen: onBulkDeleteOpen,
    onClose: onBulkDeleteClose,
    onOpenChange: onBulkDeleteOpenChange,
  } = useDisclosure({ id: "customer-modal-3" });

  // Add labels modal
  const {
    isOpen: isAddLabelsOpen,
    onOpen: onAddLabelsOpen,
    onClose: onAddLabelsClose,
    onOpenChange: onAddLabelsOpenChange,
  } = useDisclosure({ id: "customer-modal-4" });

  // Send email campaign modal
  const {
    isOpen: isSendEmailOpen,
    onOpen: onSendEmailOpen,
    onClose: onSendEmailClose,
    onOpenChange: onSendEmailOpenChange,
  } = useDisclosure({ id: "customer-modal-5" });

  return {
    // Main filter modal
    mainModal: {
      isOpen,
      onOpen,
      onOpenChange,
      onClose,
    },

    // Delete confirmation modal for single customer
    confirmationModal: {
      isOpen: isConfirmationOpen,
      onOpen: onConfirmationOpen,
      onClose: onConfirmationClose,
      onOpenChange: onConfirmationOpenChange,
    },

    // Bulk delete customers modal
    bulkDeleteModal: {
      isOpen: isBulkDeleteOpen,
      onOpen: onBulkDeleteOpen,
      onClose: onBulkDeleteClose,
      onOpenChange: onBulkDeleteOpenChange,
    },

    // Add labels modal
    addLabelsModal: {
      isOpen: isAddLabelsOpen,
      onOpen: onAddLabelsOpen,
      onClose: onAddLabelsClose,
      onOpenChange: onAddLabelsOpenChange,
    },

    // Send email campaign modal
    sendEmailModal: {
      isOpen: isSendEmailOpen,
      onOpen: onSendEmailOpen,
      onClose: onSendEmailClose,
      onOpenChange: onSendEmailOpenChange,
    },
  };
};
