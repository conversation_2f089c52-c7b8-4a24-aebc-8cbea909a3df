import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import { OrderFilters } from "../../types/orderType";
import { GetProductsType } from "../../types/productType";

const INITIAL_VISIBLE_COLUMNS = [
	"orderNo",
	"userData.firstName",
	"createdAt",
	"paymentStatus",
	"fulfillmentStatus",
	"orderPrice",
	"itemcount",
];

export interface OrderOption {
	// Original order form fields
	options: string[];
	selectedProductId: string | null;
	setSelectedProductId: (id: string | null) => void;
	selectedCustomerId: string | null;
	setSelectedCustomerId: (id: string | null) => void;
	selectedDeliveryMethod: string | null;
	setSelectedDeliveryMethod: (method: string | null) => void;

	// Orders listing state
	orderVisibleColumns: Set<string>;
	setOrderVisibleColumns: (columns: Set<string>) => void;
	selectedOrderKeys: Set<string> | "all";
	setSelectedOrderKeys: (keys: Set<string> | "all") => void;
	orderFilterText: string;
	setOrderFilterText: (text: string) => void;
	orderFilters: OrderFilters;
	setOrderFilters: (filters: Partial<OrderFilters>) => void;

	// Order tags state
	selectedOrderTagId: string | null;
	setSelectedOrderTagId: (id: string | null) => void;

	// Cart items state
	cartItem: GetProductsType | null;
	setCartItem: (item: GetProductsType | null) => void;
}

const initialState: Omit<
	OrderOption,
	| "setSelectedProductId"
	| "setSelectedCustomerId"
	| "setSelectedDeliveryMethod"
	| "setOrderVisibleColumns"
	| "setSelectedOrderKeys"
	| "setOrderFilterText"
	| "setOrderFilters"
	| "setSelectedOrderTagId"
	| "setCartItem"
> = {
	// Original order form fields
	options: [],
	selectedProductId: null,
	selectedCustomerId: null,
	selectedDeliveryMethod: null,

	// Orders listing state
	orderVisibleColumns: new Set(INITIAL_VISIBLE_COLUMNS),
	selectedOrderKeys: new Set([]),
	orderFilterText: "",
	orderFilters: {
		paymentStatus: null,
		fulfillmentStatus: null,
		searchTerm: null,
		startDate: null,
		endDate: null,
		tagIds: null,
		isManual: null,
		isArchived: null,
		orderNo: null,
		userId: null,
		maxTotalPrice: null,
		minTotalPrice: null,
	},

	// Order tags state
	selectedOrderTagId: null,

	// Cart items state
	cartItem: null,
};

export const createOrdersSlice: StateCreator<OrderOption, [], [["zustand/immer", never]]> = immer(
	(set) => ({
		...initialState,

		// Original order form methods
		setSelectedProductId: (id) => {
			set((state) => {
				state.selectedProductId = id;
			});
		},

		setSelectedCustomerId: (id) => {
			set((state) => {
				state.selectedCustomerId = id;
			});
		},
		setSelectedDeliveryMethod: (method) => {
			set((state) => {
				state.selectedDeliveryMethod = method;
			});
		},

		setOrderVisibleColumns: (columns) => {
			set((state) => {
				state.orderVisibleColumns = columns;
			});
		},
		setSelectedOrderKeys: (keys) => {
			set((state) => {
				state.selectedOrderKeys = keys;
			});
		},
		setOrderFilterText: (text) => {
			set((state) => {
				state.orderFilterText = text;
			});
		},
		setOrderFilters: (filters) => {
			set((state) => {
				state.orderFilters = { ...state.orderFilters, ...filters };
			});
		},

		setSelectedOrderTagId: (id) => {
			set((state) => {
				state.selectedOrderTagId = id;
			});
		},
		setCartItem: (item) => {
			set((state) => {
				state.cartItem = item;
			});
		},
	})
);
