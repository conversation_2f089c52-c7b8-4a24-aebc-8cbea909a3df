import { immer } from "zustand/middleware/immer";
import UserType from "../../types/userType";
import { StateCreator } from "zustand";

export interface UserSlice {
  user: UserType | null;
  setUser: (userData: UserType) => void;
  logOut: () => void;
}

const initialState = {
  user: null,
};
export const createUserSlice: StateCreator<
  UserSlice,
  [],
  [["zustand/immer", never]]
> = immer((set) => ({
  ...initialState,
  setUser: (userData: UserType) =>
    set((state) => {
      console.log(userData, "userdata");
      state.user = userData;
    }),

  logOut: () => {
    set((state) => {
      state.user = null;
      localStorage.setItem("persist-data", "");
    });
  },
}));
