import { addToast, BreadcrumbI<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Chip, Spinner } from "@heroui/react";
import { CartItem, FulfillmentStatus, OrderItem, PaymentStatus } from "../types/orderType";
import { useNavigate, useParams } from "react-router-dom";
import CardWrapper from "../components/CardWrapper";
import ProductList from "../components/orders/ProductList";
import PaymentDetail from "../components/orders/PaymentDetail";
import { useQuery } from "@apollo/client";
import { FETCH_ORDER_BY_ID } from "../graphql/orders";
import { formatDateNative, formatTimeTo12Hour } from "../helpers/formatters";

const OrderDetails = () => {
	const navigate = useNavigate();
	const { id } = useParams();
	const { data, loading, error } = useQuery(FETCH_ORDER_BY_ID, {
		variables: { getOrderByIdId: id },
	});

	if (error) {
		addToast({
			title: "Failed to load order",
			color: "danger",
			shouldShowTimeoutProgress: true,
		});
	}

	if (loading) {
		return (
			<div className="p-4 flex items-center justify-center w-full h-screen">
				<Spinner className="text-xl" />
			</div>
		);
	}

	const orderData = data?.getOrderById;
	console.log(orderData);
	return (
		<div className="pb-10">
			{/* Breadcrumbs and header */}
			<div className="grid py-2 grid-cols-2 justify-end items-center border-b mb-5 ml-1 sticky top-[3.15rem] bg-white dark:bg-transparent left-0 z-30">
				<div className="md:px-3">
					<Breadcrumbs>
						<BreadcrumbItem onClick={() => navigate("/admin/sales/payments")}>
							Orders
						</BreadcrumbItem>
						<BreadcrumbItem>Order {orderData?.orderNo} </BreadcrumbItem>
					</Breadcrumbs>
				</div>
				<div className="flex gap-x-4 w-full justify-end">
					<Button
						size="sm"
						radius="full"
						color="primary"
						variant="ghost"
						onPress={() => navigate("/admin/sales/payments")}
					>
						Back
					</Button>
				</div>
			</div>
			{/* Main content */}
			<div className="mb-4 pl-2 md:pl-1">
				<h1 className="text-2xl flex items-center gap-3 font-semibold ">
					<span>Order {orderData?.orderNo ? `#${orderData?.orderNo}` : "-"}</span>
					<Chip
						className="capitalize"
						color={orderData?.paymentStatus === PaymentStatus.PAID ? "success" : "danger"}
						size="sm"
						variant="flat"
					>
						{orderData?.paymentStatus}
					</Chip>
					<Chip
						className="capitalize"
						color={
							orderData?.fulfillmentStatus === FulfillmentStatus.FULFILLED ? "success" : "danger"
						}
						size="sm"
						variant="flat"
					>
						{orderData?.fulfillmentStatus}
					</Chip>
				</h1>
				<p>
					Placed on{" "}
					{orderData?.createdAt
						? `${formatDateNative(orderData?.createdAt)} at ${formatTimeTo12Hour(
								orderData?.createdAt
						  )}`
						: "-"}
				</p>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-12 gap-4 dark:text-white">
				{/* Left column */}
				<div className="space-y-4 md:col-span-8">
					<CardWrapper title={`Items (${orderData?.itemcount})`}>
						<p className="py-2 border-t border-b">Products to ship</p>
						{orderData?.cart.map((item: CartItem) => (
							<ProductList
								key={item.productId}
								imgsrc={item.asset.path || "/images/placeholder.svg"}
								productName={item.name}
								price={item.price}
								qty={item.qty}
								finalPrice={item.finalPrice}
							/>
						))}
					</CardWrapper>

					<CardWrapper title={"Payment Info"}>
						<PaymentDetail
							paymentStatus={orderData?.paymentStatus}
							totalAmount={orderData?.totalprice}
							itemsAmount={orderData?.orderPrice}
							shippingAmount={orderData?.shippingAmount}
							couponAmount={orderData?.coupon?.amount}
							taxAmount={orderData?.taxAmount}
							couponName={orderData?.coupon?.couponName}
							customFee={orderData?.customFee || []}
							customDiscount={orderData?.customDiscount || []}
						/>
					</CardWrapper>
				</div>
				<div className="md:col-span-4">
					<CardWrapper title={"Order Info"}>
						<div className="pb-4">
							<p className="underline text-sm ">Contact Info</p>
							<p className="text-primary">
								{orderData?.userData
									? `${orderData?.userData.firstName} ${orderData?.userData.lastName}`
									: "-"}
							</p>
							<p>{orderData?.userData?.email || "-"}</p>
						</div>
						<div className="border-t border-b py-4 ">
							<p className="text-sm underline">Delivery method</p>
							<p>Standard Shipping</p>
							<p>5-7 Business Days</p>
						</div>
						<div className="py-4">
							<p className="text-sm underline">Shipping Address</p>
							{orderData?.shippingAddress ? (
								<>
									<p className="Capitalize">
										Address Type: {orderData?.shippingAddress?.addressType}
									</p>
									<p>
										{orderData?.shippingAddress?.flat}
										{orderData?.shippingAddress?.addressline1},{" "}
										{orderData?.shippingAddress?.addressline2},{" "}
										{orderData?.shippingAddress?.landmark}, {orderData?.shippingAddress?.city},{" "}
										{orderData?.shippingAddress?.states}, {orderData?.shippingAddress?.pincode}{" "}
										{orderData?.shippingAddress?.country}
									</p>
									<p>Phone: {orderData?.shippingAddress?.phone}</p>
								</>
							) : null}
						</div>
						<div className="border-t py-4">
							<p className="text-sm underline">Billing Address</p>
							{orderData?.billingAddress ? (
								<>
									<p className="Capitalize">
										Address Type: {orderData?.billingAddress?.addressType}
									</p>
									<p>
										{orderData?.billingAddress?.flat}
										{orderData?.billingAddress?.addressline1},{" "}
										{orderData?.billingAddress?.addressline2}, {orderData?.billingAddress?.landmark}
										, {orderData?.billingAddress?.city}, {orderData?.billingAddress?.states},{" "}
										{orderData?.billingAddress?.pincode} {orderData?.billingAddress?.country}
									</p>
									<p>Phone: {orderData?.billingAddress?.phone}</p>
								</>
							) : null}
						</div>
					</CardWrapper>
				</div>
			</div>
		</div>
	);
};

export default OrderDetails;
