import { immer } from "zustand/middleware/immer";
import { StateCreator } from "zustand";
import { PaymentFilters } from "../../types/paymentTypes";

const INITIAL_VISIBLE_COLUMNS = [
  "date",
  "customer",
  "product",
  "paymentMethod",
  "status",
  "amount",
];

export interface PaymentsSlice {
  paymentVisibleColumns: Set<string>;
  setPaymentVisibleColumns: (columns: Set<string>) => void;

  selectedPaymentKeys: Set<string> | "all";
  setSelectedPaymentKeys: (keys: Set<string> | "all") => void;

  paymentFilterText: string;
  setPaymentFilterText: (text: string) => void;

  paymentFilters: PaymentFilters;
  setPaymentFilters: (filters: Partial<PaymentFilters>) => void;
}

const initialState: Omit<
  PaymentsSlice,
  | "setPaymentVisibleColumns"
  | "setSelectedPaymentKeys"
  | "setPaymentFilterText"
  | "setPaymentFilters"
> = {
  // Payments listing state
  paymentVisibleColumns: new Set(INITIAL_VISIBLE_COLUMNS),
  selectedPaymentKeys: new Set([]),
  paymentFilterText: "",
  paymentFilters: {
    status: null,
    paymentMethod: null,
    search: "",
    dateRange: {
      startDate: null,
      endDate: null,
    },
  },
};

export const paymentsSlice: StateCreator<
  PaymentsSlice,
  [],
  [["zustand/immer", never]]
> = immer((set) => ({
  ...initialState,

  setPaymentVisibleColumns: (columns) => {
    set((state) => {
      state.paymentVisibleColumns = columns;
    });
  },

  setSelectedPaymentKeys: (keys) => {
    set((state) => {
      state.selectedPaymentKeys = keys;
    });
  },

  setPaymentFilterText: (text) => {
    set((state) => {
      state.paymentFilterText = text;
    });
  },

  setPaymentFilters: (filters) => {
    set((state) => {
      state.paymentFilters = { ...state.paymentFilters, ...filters };
    });
  },
}));
