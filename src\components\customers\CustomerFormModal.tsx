import { useRef, useCallback, useMemo } from "react";
import ModalComponent from "../ModalComponent";
import CustomerForm from "./CustomerForm";
import { CustomerFormData } from "../../validation/customerValidationSchema";
import { CustomerMemberStatus, CustomerType } from "../../types/customerType";
import { useMutationHandler } from "../../hooks/useMutationStatusHandler";
import { useMutation } from "@apollo/client";
import { GET_CUSTOMERS, SAVE_CUSTOMERS } from "../../graphql/customers";

interface CustomerFormModalProps {
	isOpen: boolean;
	onOpenChange: () => void;
	initialData?: CustomerType | null;
	isEditMode?: boolean;
	isLoading?: boolean;
}

const CustomerFormModal = ({
	isOpen,
	onOpenChange,
	initialData = null,
	isEditMode = false,
	isLoading = false,
}: CustomerFormModalProps) => {
	const formRef = useRef<HTMLFormElement>(null);

	// Save customer mutation
	const [
		saveAdminSideUser,
		{ data: saveCustomerData, loading: saveCustomerLoading, error: saveCustomerError },
	] = useMutation(SAVE_CUSTOMERS, {
		refetchQueries: [GET_CUSTOMERS, "GetUsersByFilters"],
	});

	// Handle save customer mutation response
	useMutationHandler({
		data: saveCustomerData,
		loading: saveCustomerLoading,
		error: saveCustomerError,
		successMessage: "Customer saved successfully",
	});

	// Memoized input transformation
	const transformFormData = useCallback(
		(data: CustomerFormData) => ({
			firstName: data.firstName,
			lastName: data.lastName,
			email: data.email || "",
			phoneNumber: data.phoneNumber || "",
			gender: data.gender,
			dateOfBirth: data.dateOfBirth || "",
			emailSubscribedStatus: data.emailSubscribedStatus || CustomerMemberStatus.NEVER_SUBSCRIBED,
			labelIds: data.labelIds || [],
			addresses: data.addresses,
		}),
		[]
	);

	// Memoized submit handler
	const handleSubmit = useCallback(
		(data: CustomerFormData) => {
			const input = transformFormData(data);

			if (!isEditMode) {
				saveAdminSideUser({
					variables: { input },
				}).then(() => {
					onOpenChange();
				});
			} else {
				saveAdminSideUser({
					variables: {
						input,
						saveAdminSideUserId: initialData?._id,
					},
				}).then(() => {
					onOpenChange();
				});
			}
		},
		[isEditMode, saveAdminSideUser, onOpenChange, initialData?._id, transformFormData]
	);

	// Memoized form submission handler
	const handleFormSubmit = useCallback(() => {
		if (formRef.current) {
			formRef.current.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }));
		}
	}, []);

	// Memoized modal header
	const modalHeader = useMemo(
		() => (isEditMode ? "Edit customer" : "Create a new contact"),
		[isEditMode]
	);

	// Memoized class names
	const modalClassName = useMemo(
		() => ({
			saveColor: "primary" as const,
		}),
		[]
	);

	return (
		<ModalComponent
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			modalHeader={modalHeader}
			saveButtonText="Save"
			size="2xl"
			isLoading={isLoading}
			onPress={handleFormSubmit}
			className={modalClassName}
		>
			<div className="max-h-[70vh] scrollbar overflow-y-auto px-1">
				<CustomerForm
					initialData={initialData}
					onSubmit={handleSubmit}
					isLoading={isLoading}
					formRef={formRef as React.RefObject<HTMLFormElement>}
				/>
			</div>
		</ModalComponent>
	);
};

export default CustomerFormModal;
