import * as z from "zod";

export const productValidationSchema = z.object({
  images: z
    .instanceof(FileList)
    .refine((files) => files.length > 0, {
      message: "At least one image or video is required",
    })
    .optional(),
  videos: z.instanceof(FileList).optional(),
  assets: z
    .array(
      z.object({
        altText: z.string(),
        isFeatured: z.boolean(),
        path: z.string(),
        type: z.enum(["IMAGE", "VIDEO"]),
      })
    )
    .optional(),
  name: z.string().min(1, { message: "Name is required" }),
  ribbon: z
    .object({
      name: z.string(),
      ribbonId: z.string(),
    })
    .optional(),
  description: z.string().min(1, { message: "Description is required" }),
  price: z.number().min(1, { message: "Price is required" }),
  isOnSale: z.boolean().optional(),
  customTexts: z
    .array(
      z.object({
        title: z.string(),
        charLimit: z.number(),
        isRequired: z.boolean(),
      })
    )
    .optional(),
  allowCustomText: z.boolean().optional(),
  productOptions: z
    .array(
      z.object({
        optionName: z.string(),
        choices: z.array(
          z.object({
            name: z.string(),
            images: z.array(z.string()),
          })
        ),
        showInProductPageAs: z.string(),
      })
    )
    .optional(),
  categoryIds: z
    .array(z.string().min(1, { message: "Categories is required" }))
    .min(1, { message: "Categories is required" }),
  trackInventory: z.boolean().optional(),
  discountedPrice: z.number().optional(),
  saleValue: z.number({ message: "Enter only numbers" }).optional(),
  saleType: z
    .enum(["PERCENT", "RUPEE"], {
      required_error: "Sale type is required",
      invalid_type_error: "Sale type must be either 'PERCENTAGE' or 'RUPEE'",
    })
    .optional(),
  costOfGoods: z.number().min(1, { message: "Cost of Goods is required" }),
});
