import { useState } from "react";
import { Input, Tabs, Tab, Chip, Tooltip } from "@heroui/react";
import { ChromePicker } from "react-color";
import { CiViewList } from "react-icons/ci";
import { MdOutlineInvertColors } from "react-icons/md";
import { FaCircle } from "react-icons/fa";
import { BiX } from "react-icons/bi";
import { IoInformationCircle } from "react-icons/io5";
import { ProductOptions } from "../../types/productType";

interface ProductOptionsProps {
  productOptions: ProductOptions;
  setProductOptions: (options: ProductOptions) => void;
  errors: any;
  handleTabChange: (key: string) => void;
  removeTag: (index: number) => void;
  handleColorChange: (color: any, index: number) => void;
  handleKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  input: string;
  setInput: (input: string) => void;
}

const AddProductOptions: React.FC<ProductOptionsProps> = ({
  productOptions,
  setProductOptions,
  errors,
  handleTabChange,
  removeTag,
  handleColorChange,
  handleKeyDown,
  input,
  setInput,
}) => {
  const [displayColorPicker, setDisplayColorPicker] = useState(false);
  const [colorPickerIndex, setColorPickerIndex] = useState<number | null>(null);

  return (
    <div className="flex flex-col gap-y-4">
      <div className="grid grid-cols-2  md:grid-cols-12 place-items-center gap-4">
        <Input
          label="Type in an option name"
          size="sm"
          isRequired
          value={productOptions.optionName ?? ""}
          onChange={(e) =>
            setProductOptions({
              ...productOptions,
              optionName: e.target.value,
            })
          }
          labelPlacement="outside"
          errorMessage={errors.productOptions?.[0]?.optionName?.message}
          isInvalid={!!errors.productOptions?.[0]?.optionName}
          classNames={{
            inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md",
            label: "text-base ",
          }}
          className="col-span-full md:col-span-7"
          placeholder="e.g., Size or Weight"
        />
        <div className="cols-span-full  md:col-span-5 md:-mt-[3px] pl-2 md:pl-0">
          <p className="text-base  text-nowrap pb-1">
            Show in product page as:
          </p>
          <Tabs
            aria-label="Options"
            radius="sm"
            size="sm"
            color="primary"
            classNames={{ tabList: "rounded-md" }}
            selectedKey={productOptions.showInProductPageAs ?? "List"}
            onSelectionChange={(key) => handleTabChange(key as string)}
          >
            <Tab
              key="List"
              title={
                <div className="flex items-center space-x-2">
                  <CiViewList />
                  <span>List</span>
                </div>
              }
            />
            <Tab
              key="Color"
              title={
                <div className="flex items-center space-x-2">
                  <MdOutlineInvertColors />
                  <span>Color</span>
                </div>
              }
            />
          </Tabs>
        </div>
      </div>
      <div className="tag-input">
        <div className="tags">
          {productOptions.choices.map((choice, choiceIndex) => (
            <div key={choiceIndex} className="relative">
              <Chip
                className="tag"
                radius="full"
                startContent={
                  productOptions.showInProductPageAs === "Color" ? (
                    <FaCircle
                      onClick={() => {
                        setDisplayColorPicker(true);
                        setColorPickerIndex(choiceIndex);
                      }}
                      className="text-[7px] ml-1 rounded-full hover:cursor-pointer"
                      style={{ color: choice.name }}
                    />
                  ) : null
                }
                onClose={() => removeTag(choiceIndex)}
                endContent={<BiX className="text-sm" />}
              >
                {choice.name}
              </Chip>
              {displayColorPicker && colorPickerIndex === choiceIndex && (
                <div className="absolute z-10">
                  <ChromePicker
                    color={choice.name}
                    onChange={(color) => {
                      handleColorChange(color, choiceIndex);
                      setDisplayColorPicker(false);
                      setColorPickerIndex(null);
                    }}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
        <div className="flex gap-x-1 items-center hover:cursor-pointer">
          <label htmlFor="choices">Type in choices for this option</label>
          <Tooltip content="Every time you want to save a choice, press Enter to add it">
            <IoInformationCircle />
          </Tooltip>
        </div>
        <input
          type="text"
          id="choices"
          value={input}
          className="input-tag mt-1"
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Press enter to add tags"
        />
      </div>
    </div>
  );
};

export default AddProductOptions;
