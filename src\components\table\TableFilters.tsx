import { Accordion, AccordionItem, Button, Radio, RadioGroup } from "@heroui/react";

import { ProductType, StatusTypes, StockStatus } from "../../types/commonTypes";

type TableFiltersProps = {
	filters: {
		status: StatusTypes | null;
		stockStatus?: StockStatus | null;
		productType?: ProductType | null;
	};
	setFilters: (value: {
		status?: StatusTypes | null;
		stockStatus?: StockStatus | null;
		productType?: ProductType | null;
	}) => void;
};

const TableFilters = ({ filters, setFilters }: TableFiltersProps) => {
	console.log(filters);
	return (
		<div>
			<h5>Filter By:</h5>
			<Accordion isCompact selectionMode="multiple" defaultExpandedKeys={["1", "2", "3"]}>
				<AccordionItem
					classNames={{
						title: "text-sm font-semibold",
					}}
					key="1"
					aria-label="Accordion 1"
					title="Status"
				>
					<RadioGroup
						aria-label="Filter by Status"
						size="sm"
						classNames={{ label: "text-sm" }}
						onValueChange={(value) => setFilters({ status: value as StatusTypes })}
						value={filters.status}
					>
						{Object.values(StatusTypes).map((status) => (
							<Radio key={status} value={status}>
								{status}
							</Radio>
						))}
					</RadioGroup>
				</AccordionItem>
				<AccordionItem
					key="2"
					aria-label="Filter by Stock Status"
					title="Stock Status"
					classNames={{
						title: "text-sm font-semibold",
					}}
				>
					<RadioGroup
						aria-label="Filter by Stock Status"
						size="sm"
						classNames={{ label: "text-sm" }}
						onValueChange={(value) => setFilters({ stockStatus: value as StockStatus })}
						value={filters.stockStatus}
					>
						{Object.values(StockStatus).map((status) => (
							<Radio key={status} value={status}>
								{status === "IN_STOCK" ? "In Stock" : "Out of Stock"}
							</Radio>
						))}
					</RadioGroup>
				</AccordionItem>
				<AccordionItem
					key="3"
					aria-label="Filter by Product Type"
					title="Product Type"
					classNames={{
						title: "text-sm font-semibold",
					}}
				>
					<RadioGroup
						aria-label="Filter by Stock Status"
						size="sm"
						classNames={{ label: "text-sm" }}
						onValueChange={(value) => setFilters({ productType: value as ProductType })}
						value={filters.productType}
					>
						{Object.values(ProductType).map((product) => (
							<Radio key={product} value={product}>
								{product}
							</Radio>
						))}
					</RadioGroup>
				</AccordionItem>
			</Accordion>
			<Button
				variant="ghost"
				size="sm"
				radius="full"
				color="primary"
				className="mt-10"
				onPress={() => setFilters({ status: null, stockStatus: null, productType: null })}
			>
				Clear All Filters{" "}
			</Button>
		</div>
	);
};

export default TableFilters;
