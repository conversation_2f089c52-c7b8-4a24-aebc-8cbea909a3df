import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";

type DrawerContentTypes = {
  isOpen: boolean; // function to open the drawer
  onOpenChange: () => void;
  drawerHeader?: string;
  children: React.ReactNode; // drawer content
  handleAction: () => void; // function to handle the action when the submit button is clicked
  isLoading?: boolean; // is loading the action when the submit button is clicked
};
const TopDrawer = ({
  isOpen,
  onOpenChange,
  drawerHeader = "Title",
  children,
  handleAction = () => {}, // function to handle the action when the submit button is clicked
  isLoading = false,
}: DrawerContentTypes) => {
  return (
    <Drawer isOpen={isOpen} backdrop="opaque" onOpenChange={onOpenChange}>
      <DrawerContent>
        {(onClose) => (
          <>
            <DrawerHeader className="flex flex-col gap-1">
              <h4> {drawerHeader}</h4>
            </DrawerHeader>
            <DrawerBody>{children}</DrawerBody>
            <DrawerFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Close
              </Button>
              <Button
                color="primary"
                isLoading={isLoading}
                onPress={handleAction}
              >
                Submit
              </Button>
            </DrawerFooter>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
};

export default TopDrawer;
