import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>,
  Input,
  Radio,
  RadioGroup,
  Spinner,
  Textarea,
} from "@heroui/react";
import { useRef, useState } from "react";
import { B<PERSON><PERSON>heck, BiPlus, BiTrash, BiX } from "react-icons/bi";

// Define types for email campaigns
export interface EmailCampaignType {
  _id: string;
  name: string;
  subject?: string;
  content?: string;
}

// Mock data for email campaigns (replace with actual API calls later)
const mockCampaigns: EmailCampaignType[] = [
  { _id: "1", name: "Welcome Email", subject: "Welcome to our store!" },
  { _id: "2", name: "Monthly Newsletter", subject: "Check out our latest products" },
  { _id: "3", name: "Special Offer", subject: "Exclusive discount for you" },
  { _id: "4", name: "Abandoned Cart Reminder", subject: "You left items in your cart" },
];

const SendEmailCampaignModal = () => {
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(null);
  const [createNewCampaign, setCreateNewCampaign] = useState(false);
  const [newCampaign, setNewCampaign] = useState<{
    name: string;
    subject: string;
    content: string;
  }>({
    name: "",
    subject: "",
    content: "",
  });
  
  // Mock loading state
  const [loading, setLoading] = useState(false);
  
  // Mock data (replace with actual API call)
  const [campaigns, setCampaigns] = useState<EmailCampaignType[]>(mockCampaigns);

  // Mock mutation handlers
  const handleDeleteCampaign = (campaignId: string) => {
    // API_CHANGE: Replace with actual delete mutation
    setCampaigns(campaigns.filter(campaign => campaign._id !== campaignId));
  };

  const handleNewCampaign = () => {
    // API_CHANGE: Replace with actual create mutation
    if (newCampaign.name.trim() && newCampaign.subject.trim()) {
      const newCampaignItem: EmailCampaignType = {
        _id: `new-${Date.now()}`,
        name: newCampaign.name,
        subject: newCampaign.subject,
        content: newCampaign.content,
      };
      setCampaigns([...campaigns, newCampaignItem]);
      setNewCampaign({
        name: "",
        subject: "",
        content: "",
      });
      setCreateNewCampaign(false);
    }
  };

  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  return (
    <div className="flex flex-col gap-y-2">
      <p>
        Select an email campaign to send to the selected customers or create a new one.
      </p>
      <Divider />
      
      {!createNewCampaign ? (
        <>
          <RadioGroup
            value={selectedCampaignId || ""}
            onValueChange={setSelectedCampaignId}
            classNames={{ wrapper: "flex flex-col gap-y-2" }}
            className="max-h-60 overflow-y-scroll scrollbar"
            ref={scrollContainerRef}
          >
            {campaigns.map((campaign: EmailCampaignType) => (
              <div
                key={campaign._id}
                className="group flex w-full justify-between"
              >
                <Radio value={campaign._id}>
                  <div className="text-small">
                    <div>{campaign.name}</div>
                    <small className="text-textPlaceHolder">{campaign.subject}</small>
                  </div>
                </Radio>
                <div className="group-hover:block opacity-0 group-hover:opacity-100">
                  <Button
                    isIconOnly
                    startContent={<BiTrash />}
                    onPress={() => handleDeleteCampaign(campaign._id)}
                    className="bg-lightPrimary text-primary border-none"
                    size="sm"
                    variant="ghost"
                    radius="full"
                  />
                </div>
              </div>
            ))}
            {loading && <Spinner className="mt-4" />}
          </RadioGroup>

          <button
            className="text-primary text-small flex items-center gap-x-2 mt-2"
            onClick={() => setCreateNewCampaign(true)}
          >
            <BiPlus className="text-lg" />
            <span>Create New Campaign</span>
          </button>
        </>
      ) : (
        <div className="flex flex-col gap-y-3">
          <Input
            label="Campaign Name"
            placeholder="Enter campaign name"
            value={newCampaign.name}
            onValueChange={(value) => setNewCampaign({...newCampaign, name: value})}
          />
          
          <Input
            label="Email Subject"
            placeholder="Enter email subject"
            value={newCampaign.subject}
            onValueChange={(value) => setNewCampaign({...newCampaign, subject: value})}
          />
          
          <Textarea
            label="Email Content"
            placeholder="Enter email content"
            value={newCampaign.content}
            onValueChange={(value) => setNewCampaign({...newCampaign, content: value})}
            minRows={5}
          />
          
          <div className="flex justify-end gap-x-2 mt-2">
            <Button
              variant="flat"
              color="danger"
              onPress={() => {
                setCreateNewCampaign(false);
                setNewCampaign({
                  name: "",
                  subject: "",
                  content: "",
                });
              }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleNewCampaign}
              isDisabled={!newCampaign.name.trim() || !newCampaign.subject.trim()}
            >
              Create Campaign
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SendEmailCampaignModal;
