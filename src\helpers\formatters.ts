export const formatStockStatus = (stockStatus: string) => {
  const stockStatusMap: Record<string, string> = {
    IN_STOCK: "In Stock",
    OUT_OF_STOCK: "Out of Stock",
  };
  return stockStatusMap[stockStatus] || stockStatus;
};

export function formatDateNative(dateString: string, locale = "en-US") {
  const date = new Date(dateString);
  const formatter = new Intl.DateTimeFormat(locale, {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
  return formatter.format(date);
}

export function formatTimeTo12Hour(time24: string) {
  const [hourStr, minuteStr] = time24.split(":");
  let hour = parseInt(hourStr, 10);
  const minute = parseInt(minuteStr, 10);
  const period = hour >= 12 ? "PM" : "AM";
  hour = hour % 12 || 12; // Convert '0' to '12' for 12 AM
  return `${hour}:${minute.toString().padStart(2, "0")} ${period}`;
}
