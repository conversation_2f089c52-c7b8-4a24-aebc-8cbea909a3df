export enum PaymentStatus {
  SUCCESSFUL = "SUCCESSFUL",
  DECLINED = "DECLINED",
  PENDING = "PENDING",
  REFUNDED = "REFUNDED",
}

export interface PaymentCustomer {
  _id: string;
  name: string;
  email: string;
  phone?: string;
}

export interface PaymentProduct {
  _id: string;
  name: string;
  price: number;
  quantity: number;
}

export interface PaymentType {
  _id: string;
  date: string;
  customer: PaymentCustomer;
  product: PaymentProduct;
  paymentMethod: string;
  status: PaymentStatus;
  amount: number;
}

export interface GetPaymentsResponse {
  payments: PaymentType[];
  totalCount: number;
}

export interface PaymentFilters {
  status: PaymentStatus | null;
  paymentMethod: string | null;
  search: string;
  dateRange?: {
    startDate: string | null;
    endDate: string | null;
  };
}

export interface ColumnType {
  name: string;
  uid: string;
  sortable?: boolean;
}
