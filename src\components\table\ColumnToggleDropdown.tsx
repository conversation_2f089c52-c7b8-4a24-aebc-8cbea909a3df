import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/react";
import { ColumnType } from "../../types/productType";
import { BiChevronDown } from "react-icons/bi";
import { capitalize } from "./TopHeader";

export const ColumnToggleDropdown = ({
	columns,
	visibleColumns,
	setVisibleColumns,
}: {
	columns: ColumnType[];
	visibleColumns: Set<string>;
	setVisibleColumns: (columns: Set<string>) => void;
}) => (
	<Dropdown>
		<DropdownTrigger className="flex !rounded-[40px]">
			<Button
				endContent={<BiChevronDown className="text-small" />}
				variant="ghost"
				size="sm"
				color="primary"
				className="border"
			>
				Columns
			</Button>
		</DropdownTrigger>
		<DropdownMenu
			aria-label="Columns"
			closeOnSelect={false}
			selectedKeys={visibleColumns}
			selectionMode="multiple"
			onSelectionChange={(keys) => {
				setVisibleColumns(new Set(Array.from(keys as Set<string>)));
			}}
		>
			{columns.map(({ uid, name }) => (
				<DropdownItem key={uid} className="capitalize">
					{capitalize(name)}
				</DropdownItem>
			))}
		</DropdownMenu>
	</Dropdown>
);
