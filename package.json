{"name": "zcommerce-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.13.1", "@heroui/react": "^2.7.4", "@heroui/use-infinite-scroll": "^2.2.7", "@heroui/use-theme": "^2.1.6", "@hookform/resolvers": "^4.1.3", "@internationalized/date": "^3.7.0", "@types/react-csv": "^1.1.10", "framer-motion": "^12.4.10", "graphql": "^16.10.0", "immer": "^10.1.1", "react": "^19.0.0", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-quill-new": "^3.3.3", "react-router-dom": "^6.30.0", "uuid": "^11.1.0", "yup": "^1.6.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}