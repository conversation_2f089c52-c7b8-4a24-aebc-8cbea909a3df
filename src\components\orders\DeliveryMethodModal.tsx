import {
  Control,
  Controller,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import {
  RadioGroup,
  Radio,
  Input,
  NumberInput,
  Switch,
  DatePicker,
  TimeInput,
  Checkbox,
} from "@heroui/react"; // Import your UI components
import {
  CalendarDate,
  Time,
  parseDate,
  parseTime,
} from "@internationalized/date"; // Import date and time utilities
import { OrderSchema } from "../../types/orderType";

type DeliveryMethodProps = {
  errors: any;
  control: Control<OrderSchema, any>;
  watch: UseFormWatch<OrderSchema>;
  setValue: UseFormSetValue<OrderSchema>;
};
const DeliveryMethodModal = ({
  errors,
  control,
  watch,
  setValue,
}: DeliveryMethodProps) => {
  //   const { control, watch, setValue } = useFormContext();
  return (
    <div>
      {/* Delivery method radio */}
      <Controller
        control={control}
        name="deliveryMethod"
        render={({ field }) => (
          <RadioGroup
            value={field.value}
            onValueChange={field.onChange}
            className="space-y-2"
          >
            <Radio value="NO_CHARGE">Don't charge</Radio>
            <Radio value="CUSTOM">Create a custom rate</Radio>
          </RadioGroup>
        )}
      />
      {errors.deliveryMethod && (
        <p className="text-red-500">{errors.deliveryMethod.message}</p>
      )}

      {watch("deliveryMethod") === "CUSTOM" && (
        <div className="mt-4 p-4 rounded-md flex flex-col gap-y-4">
          {/* Name */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Controller
              control={control}
              name="customeDeliveryOptions.name"
              render={({ field }) => (
                <Input
                  {...field}
                  isRequired
                  label="Name"
                  labelPlacement="outside"
                  size="sm"
                  classNames={{
                    inputWrapper:
                      "w-full after:h-[1px] after:bg-primary rounded-md z-10",
                    label: "text-base",
                  }}
                  placeholder="e.g. Standard shipping"
                  isInvalid={!!errors.customeDeliveryOptions?.name}
                  errorMessage={errors.customeDeliveryOptions?.name?.message}
                />
              )}
            />

            {/* Rate */}
            <Controller
              control={control}
              name="customeDeliveryOptions.rate"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <NumberInput
                  hideStepper
                  label="Rate"
                  size="sm"
                  ref={ref}
                  onValueChange={(value) => {
                    onChange(value); // Update form state
                    setValue("customeDeliveryOptions.rate", value); // Manually update the form value if needed
                  }}
                  onBlur={onBlur} // Ensure validation runs on blur
                  value={value}
                  labelPlacement="outside"
                  startContent="₹"
                  errorMessage={errors.customeDeliveryOptions?.rate?.message}
                  isInvalid={!!errors.customeDeliveryOptions?.rate?.message}
                  classNames={{
                    inputWrapper:
                      "w-full after:h-[1px] after:bg-primary rounded-md",
                    label: "text-base",
                  }}
                />
              )}
            />
          </div>

          {/* Scheduling toggle */}
          <Controller
            control={control}
            name="customeDeliveryOptions.schedule"
            render={({ field }) => (
              <div className="flex items-center gap-3">
                <label>Add scheduling</label>
                <Switch
                  size="sm"
                  checked={field.value}
                  onValueChange={field.onChange}
                />
              </div>
            )}
          />

          {/* Schedule inputs */}
          {watch("customeDeliveryOptions.schedule") && (
            <div className="flex items-center  gap-x-4">
              <Controller
                control={control}
                name="customeDeliveryOptions.date"
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    className="max-w-[284px]"
                    size="sm"
                    classNames={{
                      inputWrapper: "rounded-md",
                      label: "text-base",
                    }}
                    label="Date"
                    labelPlacement="outside"
                    onChange={(value) => {
                      if (value) {
                        const date = new CalendarDate(
                          value.year,
                          value.month,
                          value.day
                        );
                        field.onChange(date.toString());
                      }
                    }}
                    value={field.value ? parseDate(field.value) : null}
                  />
                )}
              />
              <div className="flex gap-2">
                <Controller
                  control={control}
                  name="customeDeliveryOptions.fromTime"
                  render={({ field }) => (
                    <TimeInput
                      size="sm"
                      defaultValue={new Time(11, 45)}
                      {...field}
                      onChange={(value) => {
                        if (value) {
                          field.onChange(
                            new Time(value.hour, value.minute).toString()
                          );
                        }
                      }}
                      classNames={{
                        inputWrapper: "rounded-md",
                        label: "text-base",
                      }}
                      labelPlacement="outside"
                      value={field.value ? parseTime(field.value) : null}
                      label="From"
                      isInvalid={!!errors.customeDeliveryOptions?.fromTime}
                    />
                  )}
                />
                <Controller
                  control={control}
                  name="customeDeliveryOptions.totime"
                  render={({ field }) => (
                    <TimeInput
                      size="sm"
                      defaultValue={new Time(11, 45)}
                      {...field}
                      onChange={(value) => {
                        if (value) {
                          field.onChange(
                            new Time(value.hour, value.minute).toString()
                          );
                        }
                      }}
                      classNames={{
                        inputWrapper: "rounded-md",
                        label: "text-base",
                      }}
                      labelPlacement="outside"
                      value={field.value ? parseTime(field.value) : null}
                      label="To"
                      isInvalid={!!errors.customeDeliveryOptions?.totime}
                    />
                  )}
                />
              </div>
            </div>
          )}

          {/* Pickup checkbox */}
          <Controller
            control={control}
            name="customeDeliveryOptions.pickup"
            render={({ field }) => (
              <div className="flex items-center gap-2 mt-3">
                <Checkbox
                  isSelected={field.value}
                  onBlur={field.onBlur}
                  ref={field.ref}
                  onChange={(value) => field.onChange(value)}
                  aria-label="checkbox"
                  className="checkboxMenu"
                />
                <label>This is a pickup order</label>
              </div>
            )}
          />
        </div>
      )}
    </div>
  );
};

export default DeliveryMethodModal;
