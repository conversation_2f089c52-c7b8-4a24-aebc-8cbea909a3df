import { Input, NumberInput, Switch, Tab, Tabs } from "@heroui/react";
import React from "react";
import {
  Controller,
  UseFormResetField,
  UseFormSetValue,
} from "react-hook-form";
import { ProductSchema, SaleTypeEnum } from "../../types/productType";

type PricingSectionProps = {
  control: any;
  errors: any;
  setFormValue: UseFormSetValue<ProductSchema>;
  watch: any;
  getValues: (name: string) => any;
  resetField: UseFormResetField<ProductSchema>;
  register: any;
};

const PricingSection = ({
  control,
  errors,
  setFormValue,
  getValues,
  watch,
  resetField,
  register,
}: PricingSectionProps) => {
  const isOnSale = watch("isOnSale");
  const price = Number(watch("price"));
  const profit = Number(watch("profit"));
  const margin = (profit / price) * 100;

  // calculating the discounted price
  const calculateDiscountedPrice = () => {
    const discount = Number(watch("saleValue"));
    const saleType = watch("saleType");
    const salePrice =
      saleType === "PERCENT"
        ? price - (price * discount) / 100
        : price - discount;
    return salePrice;
  };

  // calculating the cost of goods
  const handleCostOfGoodsChange = (
    value: React.ChangeEvent<HTMLInputElement> | number
  ) => {
    if (typeof value === "number") {
      setFormValue("costOfGoods", value); // Ensure form value updates
      const price = getValues("price");
      const profit = price - value;
      setFormValue("profit", isNaN(profit) ? 0 : profit);
    } else {
      setFormValue("costOfGoods", Number(value.target.value)); // Ensure form value updates
      const price = getValues("price");
      const profit = price - Number(value.target.value);
      setFormValue("profit", isNaN(profit) ? 0 : profit);
    }
  };

  return (
    <div className="flex flex-col gap-y-3">
      <Controller
        name="price"
        control={control}
        render={({ field: { onChange, onBlur, value, ref } }) => (
          <NumberInput
            hideStepper
            isRequired
            label="Price"
            ref={ref}
            onValueChange={(value) => {
              onChange(value); // Update form state
              setFormValue("price", value); // Manually update the form value if needed
            }}
            onBlur={onBlur} // Ensure validation runs on blur
            value={value}
            labelPlacement="outside"
            startContent="₹"
            errorMessage={errors.price?.message}
            isInvalid={!!errors.price}
            classNames={{
              inputWrapper: "w-full after:h-[1px] after:bg-primary rounded-md",
            }}
          />
        )}
      />

      <div className="flex flex-col gap-y-4">
        <Switch
          isSelected={isOnSale}
          isDisabled={getValues("price") <= 0}
          onValueChange={() => {
            setFormValue("isOnSale", !isOnSale);
            resetField("saleValue");
            resetField("discountedPrice");
          }}
          size="sm"
        >
          On Sale
        </Switch>

        {isOnSale ? (
          <div className="flex items-center gap-4">
            <Input
              label="Discount"
              {...register("saleValue", {
                onChange: () => {
                  setFormValue("discountedPrice", calculateDiscountedPrice());
                },
                setValueAs: (value: any) => (isNaN(value) ? 0 : Number(value)),
              })}
              labelPlacement="outside"
              endContent={
                <Tabs
                  classNames={{
                    tabList: "bg-lightPrimary rounded-md",
                  }}
                  aria-label="Sale Type"
                  size="sm"
                  selectedKey={watch("saleType")}
                  onSelectionChange={(value) => {
                    setFormValue("saleType", value as SaleTypeEnum);
                  }}
                >
                  <Tab key="PERCENT" title="%" />

                  <Tab key="RUPEE" title="₹" />
                </Tabs>
              }
              placeholder="12"
              errorMessage={errors.saleValue?.message}
              isInvalid={!!errors.saleValue}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
              }}
            />

            <Controller
              name="discountedPrice"
              control={control}
              defaultValue={0}
              render={({ field }) => (
                <NumberInput
                  hideStepper
                  label="Sale Price"
                  {...field}
                  onValueChange={(value) =>
                    setFormValue("discountedPrice", value)
                  }
                  labelPlacement="outside"
                  startContent={"₹"}
                  placeholder="12"
                  value={watch("discountedPrice")}
                  errorMessage={errors.discountedPrice?.message}
                  isInvalid={!!errors.discountedPrice}
                  classNames={{
                    inputWrapper:
                      "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
                  }}
                />
              )}
            />
          </div>
        ) : null}
      </div>
      <div className="grid grid-cols-2 md:grid-cols-none md:flex  gap-4 items-center mt-2">
        <Controller
          name="costOfGoods"
          control={control}
          render={({ field: { onChange, onBlur, value, ref } }) => (
            <NumberInput
              hideStepper
              isRequired
              label="Cost of Goods"
              ref={ref}
              onValueChange={(newValue) => {
                onChange(newValue); // Update form state
                setFormValue("costOfGoods", newValue); // Ensure form value updates

                const price = getValues("price");
                const profit = price - newValue;

                // Ensure profit updates immediately
                setFormValue("profit", isNaN(profit) ? 0 : profit);
              }}
              name="costOfGoods"
              onChange={handleCostOfGoodsChange}
              // onBlur={onBlur}
              value={value || 0}
              labelPlacement="outside"
              startContent="₹"
              errorMessage={errors?.costOfGoods?.message}
              isInvalid={!!errors?.costOfGoods}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
              }}
            />
          )}
        />

        <Controller
          name="profit"
          control={control}
          render={({ field }) => (
            <Input
              disabled
              {...field}
              label="Profit"
              labelPlacement="outside"
              startContent={"₹"}
              defaultValue="0"
              value={String(watch("profit")?.toFixed(2))}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-0",
              }}
            />
          )}
        />
        <Controller
          name="margin"
          control={control}
          render={({ field }) => (
            <Input
              disabled
              {...field}
              label="Margin"
              labelPlacement="outside"
              endContent={"%"}
              defaultValue="0"
              value={String(isNaN(margin) ? 0 : margin.toFixed(2))}
              classNames={{
                inputWrapper:
                  "w-full after:h-[1px] after:bg-primary rounded-md gap-0 pr-2",
              }}
            />
          )}
        />
      </div>
    </div>
  );
};

export default PricingSection;
