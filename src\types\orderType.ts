import { StatusTypes, StockStatus } from "./commonTypes";
import { CustomerAddressType } from "./customerType";
import { AssetInputType } from "./productType";
import { orderValidationSchema } from "../validation/orderValidationSchema";
import * as z from "zod";

export type DeliveryChargeType = "CUSTOM" | "NO_CHARGE";

export enum PaymentStatus {
	PAID = "PAID",
	UNPAID = "UNPAID",
	PENDING = "PENDING",
	REFUNDED = "REFUNDED",
}

export enum FulfillmentStatus {
	FULFILLED = "FULFILLED",
	UNFULFILLED = "UNFULFILLED",
	PARTIALLY_FULFILLED = "PARTIALLY_FULFILLED",
	PENDING = "PENDING",
}

export interface OrderItem {
	_id: string;
	productId: string;
	productName: string;
	quantity: number;
	price: number;
	total: number;
}

export interface OrderCustomer {
	userId: string;
	firstName: string;
	lastName: string;
	email: string;
	phone?: string;
}

export interface VariantDetails {
	priceDifference: number;
	selectedOptions: string[];
	shippingWeight: number;
	variantPrice: number;
	variantDetailId?: string;
	variantCostOfGoods?: number;
	trackInventory?: boolean;
	stockStatus?: StockStatus;
	stockQuantity?: number;
	status?: StatusTypes;
	sku?: string;
}

export interface CartItem {
	asset?: AssetInputType;
	finalPrice: number;
	name: string;
	price: number;
	productId: string;
	qty: number;
	categoryId?: string;
	variantDetail?: VariantDetails;
}

export interface OrderTableColumnType {
	_id: string;
	orderNo: string;
	createdAt: string;
	userData: OrderCustomer;
	paymentStatus: PaymentStatus;
	fulfillmentStatus: FulfillmentStatus;
	totalprice: number;
	orderPrice: number;
	items: OrderItem[];
	itemcount: number;
	cart: CartItem[];
}

export interface UserDataType {
	firstName: string;
	lastName: string;
	userId: string;
	email: string;
	phone?: string;
	countryCode?: string;
}

// Generate OrderSchema type from validation schema
export type OrderSchema = z.infer<typeof orderValidationSchema>;

export interface GetOrdersResponse {
	orders: OrderTableColumnType[];
	totalCount: number;
}

export interface OrderFilters {
	orderNo: string | null;
	userId: string | null;
	isManual: boolean | null;
	isArchived: boolean | null;
	maxTotalPrice: number | null;
	minTotalPrice: number | null;
	paymentStatus: PaymentStatus | null;
	fulfillmentStatus: FulfillmentStatus | null;
	searchTerm: string | null;
	startDate: string | null;
	endDate: string | null;
	tagIds: string[] | null;
}

export interface ColumnType {
	name: string;
	uid: string;
	sortable?: boolean;
}
