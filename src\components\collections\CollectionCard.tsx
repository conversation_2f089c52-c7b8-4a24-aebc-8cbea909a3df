import { Button, Image } from "@heroui/react";
import { BiEdit, BiTrash } from "react-icons/bi";

type CollectionCardType = {
  name: string;
  imgsrc: string;
  collectionId: string;
  handleCollectionDelete: (collectionId: string) => void;
  handleCollectionEdit: (
    collectionId: string,
    name: string,
    imgsrc: string
  ) => void;
};
const CollectionCard = ({
  name = "",
  imgsrc = "",
  collectionId = "",
  handleCollectionDelete = () => {},
  handleCollectionEdit = () => {},
}: CollectionCardType) => {
  return (
    <>
      <div className="rounded-md card-div flex justify-center relative dark:bg-slate-700">
        <div className="absolute top-2 right-3 flex gap-2 ">
          <Button
            endContent={<BiEdit className="text-small " />}
            variant="ghost"
            size="sm"
            isIconOnly
            color="primary"
            className="border-none bg-lightPrimary"
            radius="full"
            onPress={() => handleCollectionEdit(collectionId, name, imgsrc)}
          />
          <Button
            endContent={<BiTrash className="text-small " />}
            variant="ghost"
            size="sm"
            isIconOnly
            color="primary"
            className="border-none bg-lightPrimary"
            radius="full"
            onPress={() => handleCollectionDelete(collectionId)}
          />
        </div>
        <div className="w-full flex justify-center  ">
          <Image src={imgsrc} isZoomed className="h-[260px]" radius="none" />
        </div>
        <h2 className="text-white absolute bottom-5 left-3 z-10">{name}</h2>
      </div>
    </>
  );
};

export default CollectionCard;
