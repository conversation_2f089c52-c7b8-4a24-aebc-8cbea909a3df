import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { ApolloClient, InMemoryCache, ApolloProvider, createHttpLink } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";
import Signin from "./pages/Signin";
import SideBarLayout from "./layouts/SidebarLayout";
import { ToastProvider } from "@heroui/react";
import { BiX } from "react-icons/bi";
import Products from "./pages/Products";
import AddEditProducts from "./pages/AddEditProducts";
import Inventory from "./pages/Inventory";
import Collections from "./pages/Collections";
import UnderConstructionPage from "./pages/UnderConstruction";
import Orders from "./pages/Orders";
import AddEditOrders from "./pages/AddEditOrders";
import Payments from "./pages/Payments";
import ViewPayments from "./pages/ViewPayments";
import Customers from "./pages/Customers";
import OrderDetails from "./pages/OrderDetails";

function getAuthenticationParams(localStorageData: string | null) {
	if (!localStorageData) {
		return { dbToken: "", authToken: "" };
	}

	const parsedData = JSON.parse(localStorageData);
	return {
		dbToken: parsedData.state.user.dbToken || "",
		authToken: parsedData.state.user.authToken || "",
	};
}

const localStorageData = localStorage.getItem("persist-data");
const { dbToken, authToken } = getAuthenticationParams(localStorageData);

// Create an HTTP link
const httpLink = createHttpLink({
	uri: "http://localhost:3001/graphql",
});

// Create an auth link to set the headers
const authLink = setContext((_, { headers }) => {
	return {
		headers: {
			...headers,
			dbToken: `Bearer ${dbToken}`,
			authToken: `Bearer ${authToken}`,
		},
	};
});

// Create the Apollo Client
export const client = new ApolloClient({
	link: authLink.concat(httpLink),
	cache: new InMemoryCache({
		typePolicies: {
			Query: {
				fields: {
					getRibbonsByFilter: {
						keyArgs: false, // Disable keyArgs to avoid separate caching for each offset
						merge(existing = { ribbons: [], totalCount: 0 }, incoming) {
							return {
								...incoming,
								ribbons: [...existing.ribbons, ...incoming.ribbons],
							};
						},
					},
					getProducts: {
						keyArgs: ["filters"],
						merge(existing = { products: [], totalCount: 0 }, incoming, { args }) {
							const mergedProducts =
								args?.offset === 0
									? incoming.products
									: [...existing.products, ...incoming.products];

							return {
								...incoming,
								products: mergedProducts,
							};
						},
					},
					getOrdersByFilters: {
						keyArgs: ["filter"], // Add filter as a keyArg to separate different filter results
						merge(existing = { data: [], totalCount: 0 }, incoming, { args }) {
							// If offset is 0 or undefined, replace the data completely
							if (!args?.offset || args.offset === 0) {
								return incoming;
							}

							// Otherwise append the new data
							return {
								...incoming,
								data: [...existing.data, ...incoming.data],
							};
						},
					},
					// getCustomers: {
					//   keyArgs: false,
					//   merge(existing = { customers: [], totalCount: 0 }, incoming) {
					//     return {
					//       ...incoming,
					//       customers: [...existing.customers, ...incoming.customers],
					//     };
					//   },
					// },
				},
			},
		},
	}),
});

const router = createBrowserRouter([
	{
		path: "/",
		element: <Signin />,
	},
	{
		path: "*",
		element: <UnderConstructionPage />,
	},
	{
		path: "/sign-in",
		element: <Signin />,
	},
	{
		path: "/admin",
		element: <SideBarLayout />,
		children: [
			{
				path: "store/products",
				element: <Products />,
				index: true,
			},
			{
				path: "store/inventory",
				element: <Inventory />,
			},
			{
				path: "store/collections",
				element: <Collections />,
			},
			{
				path: "store/add-product",
				element: <AddEditProducts method="POST" />,
			},
			{
				path: "store/edit-product/:id",
				element: <AddEditProducts method="PUT" />,
			},
			{ path: "sales/orders", element: <Orders /> },
			{ path: "sales/add-order", element: <AddEditOrders /> },
			{ path: "sales/payments", element: <Payments /> },
			{ path: "sales/payments/:id", element: <ViewPayments /> },
			{ path: "sales/orders/:id", element: <OrderDetails /> },
			{ path: "customers-leads/customers", element: <Customers /> },
		],
	},
]);

// biome-ignore lint/style/noNonNullAssertion: <explanation>
createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ApolloProvider client={client}>
			<RouterProvider
				router={router}
				future={{
					v7_startTransition: false,
				}}
			/>
			<ToastProvider
				placement="top-right"
				toastOffset={5}
				toastProps={{
					variant: "flat",
					classNames: {
						description: "rounded-none",
						base: "rounded-md",
						wrapper: "rounded-none",
						closeButton: "opacity-100 absolute right-4 top-1/2 -translate-y-1/2",
					},
					closeIcon: <BiX className="text-3xl" />,
					timeout: 2000,
				}}
			/>
		</ApolloProvider>
	</StrictMode>
);
