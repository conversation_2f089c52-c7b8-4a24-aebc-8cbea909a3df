import { Select, SelectItem } from "@heroui/react";
export interface CustomersType {
	_id: string;
	label: string;
	value: string;
	email: string;
	phoneNumber?: string;
	profileImg: string;
}
interface CustomStateSelectProps {
	selectedKeys: Set<string>;
	onSelectionChange: (selectedKey: { currentKey: string }) => void;
	ariaLabel: string;
	label?: string;
	name: string;
	renderValue: (items: any) => React.ReactNode;
	placeholder?: string;
	selectItem: (data: any) => React.ReactNode;
	isRequired?: boolean;
	isDisabled?: boolean;
	isLoading?: boolean;
	isInvalid?: boolean;
	users: CustomersType[];
}

export const CustomStateSelect = ({
	selectedKeys,
	onSelectionChange,
	ariaLabel = "Select item",
	label,
	renderValue,
	placeholder,
	selectItem,
	isRequired = false,
	name,
	users,
}: CustomStateSelectProps) => {
	return (
		<>
			<label htmlFor="customer">Add a customer to the order</label>
			<Select
				selectedKeys={selectedKeys as Iterable<string>}
				onSelectionChange={(selectedKey) => {
					onSelectionChange(selectedKey as { currentKey: string });
				}}
				isRequired={isRequired}
				errorMessage={isRequired ? "This field is required" : undefined}
				aria-label={ariaLabel}
				className="max-w-full"
				classNames={{
					label: "group-data-[filled=true]:-translate-y-5",
					trigger: "rounded-md min-h-14 border mt-1",
					listboxWrapper: "max-h-[400px]",
				}}
				name={name}
				size="sm"
				label={label}
				items={users || []}
				labelPlacement="outside"
				selectionMode="single"
				placeholder={placeholder}
				listboxProps={{
					itemClasses: {
						base: [
							"rounded-md",
							"text-default-500",
							"transition-opacity",
							"data-[hover=true]:text-foreground",
							"data-[hover=true]:bg-default-100",
							"dark:data-[hover=true]:bg-default-50",
							"data-[selectable=true]:focus:bg-default-50",
							"data-[pressed=true]:opacity-70",
							"data-[focus-visible=true]:ring-default-500",
						],
					},
				}}
				popoverProps={{
					classNames: {
						base: "before:bg-default-200",
						content: "p-0 border-small border-divider bg-background",
					},
				}}
				renderValue={(items) => {
					return items.map((item) => (
						<div key={item.key} className="flex items-center gap-2">
							{item.data && renderValue(item.data)}
						</div>
					));
				}}
				variant="bordered"
			>
				{(data) => (
					<SelectItem key={data._id} textValue={data.label}>
						{selectItem(data)}
					</SelectItem>
				)}
			</Select>
		</>
	);
};

interface CustomStateSelectProps {
	selectedKeys: Set<string>;
	onSelectionChange: (selectedKey: { currentKey: string }) => void;
	ariaLabel: string;
	label?: string;
	name: string;
	renderValue: (items: any) => React.ReactNode;
	placeholder?: string;
	selectItem: (data: any) => React.ReactNode;
	isRequired?: boolean;
	isDisabled?: boolean;
	isLoading?: boolean;
	isInvalid?: boolean;
}

interface CustomFormStateSelectProps extends CustomStateSelectProps {
	value?: string; // Added for react-hook-form integration
	onChange?: (value: string) => void; // Added for react-hook-form integration
	error?: string; // Added to display validation errors
}
// export const CustomFormStateSelect = ({
// 	selectedKeys,
// 	onSelectionChange,
// 	ariaLabel = "Select item",
// 	label,
// 	renderValue,
// 	placeholder,
// 	selectItem,
// 	isRequired = false,
// 	name,
// 	value,
// 	onChange,
// 	error,
// }: CustomFormStateSelectProps) => {
// 	return (
// 		<>
// 			<label htmlFor={name}>Add a customer to the order</label>
// 			<Select
// 				selectedKeys={selectedKeys as Iterable<string>}
// 				onSelectionChange={(selectedKey) => {
// 					const currentKey = (selectedKey as { currentKey: string }).currentKey;
// 					onSelectionChange({ currentKey });
// 					if (onChange) {
// 						onChange(currentKey);
// 					}
// 				}}
// 				isRequired={isRequired}
// 				errorMessage={error}
// 				aria-label={ariaLabel}
// 				className="max-w-full"
// 				classNames={{
// 					label: "group-data-[filled=true]:-translate-y-5",
// 					trigger: "rounded-md min-h-14 border mt-1",
// 					listboxWrapper: "max-h-[400px]",
// 				}}
// 				name={name}
// 				size="sm"
// 				label={label}
// 				items={users}
// 				labelPlacement="outside"
// 				selectionMode="single"
// 				placeholder={placeholder}
// 				listboxProps={{
// 					itemClasses: {
// 						base: [
// 							"rounded-md",
// 							"text-default-500",
// 							"transition-opacity",
// 							"data-[hover=true]:text-foreground",
// 							"data-[hover=true]:bg-default-100",
// 							"dark:data-[hover=true]:bg-default-50",
// 							"data-[selectable=true]:focus:bg-default-50",
// 							"data-[pressed=true]:opacity-70",
// 							"data-[focus-visible=true]:ring-default-500",
// 						],
// 					},
// 				}}
// 				popoverProps={{
// 					classNames: {
// 						base: "before:bg-default-200",
// 						content: "p-0 border-small border-divider bg-background",
// 					},
// 				}}
// 				renderValue={(items) => {
// 					return items.map((item) => (
// 						<div key={item.key} className="flex items-center gap-2">
// 							{item.data && renderValue(item.data)}
// 						</div>
// 					));
// 				}}
// 				variant="bordered"
// 			>
// 				{(data) => (
// 					<SelectItem key={data.id} textValue={data.name}>
// 						{selectItem(data)}
// 					</SelectItem>
// 				)}
// 			</Select>
// 		</>
// 	);
// };
