import { Button, <PERSON>, useDisclosure } from "@heroui/react";
import { useCallback, useMemo } from "react";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import TableComponent from "../components/Table";
import TopHeader from "../components/table/TopHeader";
import TopDrawer from "../components/TopDrawer";
import { useBoundStore } from "../store/store";
import { ColumnType, PaymentStatus, PaymentType } from "../types/paymentTypes";
import { RxDividerVertical } from "react-icons/rx";
import { dummyPayments } from "../data/dummyPayments";
import { BiChevronRight } from "react-icons/bi";
import { useNavigate } from "react-router-dom";

// Define columns for the payments table
export const columns: ColumnType[] = [
  { name: "Customer", uid: "customer", sortable: true },
  { name: "Date", uid: "date", sortable: true },
  { name: "Product/Service", uid: "product", sortable: false },
  { name: "Payment method", uid: "paymentMethod", sortable: false },
  { name: "Status", uid: "status", sortable: false },
  { name: "Amount", uid: "amount", sortable: true },
];

const Payments = () => {
  const { isOpen, onOpenChange, onOpen } = useDisclosure();
  const navigate = useNavigate();

  // Get state from Zustand store
  const {
    paymentVisibleColumns,
    setPaymentVisibleColumns,
    selectedPaymentKeys,
    setSelectedPaymentKeys,
    paymentFilterText,
    setPaymentFilterText,
  } = useBoundStore();

  const handleAction = () => {
    //API_CHANGE need to add the refetch logic here for the filters change
  };

  // Render cell content based on column key
  const renderCell = useCallback(
    (payment: PaymentType, columnKey: keyof PaymentType | string) => {
      const cellValue = payment[columnKey as keyof PaymentType];

      switch (columnKey) {
        case "date":
          return (
            <div className="flex flex-col">
              <p className="text-bold text-small">{payment.date}</p>
            </div>
          );
        case "customer":
          return (
            <div className="flex flex-col">
              <p className="text-bold text-small capitalize">
                {payment.customer.name}
              </p>
            </div>
          );
        case "product":
          return (
            <div className="flex flex-col">
              <p className="text-bold text-small">{payment.product.name}</p>
            </div>
          );
        case "paymentMethod":
          return (
            <div className="flex flex-col">
              <p className="text-bold text-small">{payment.paymentMethod}</p>
            </div>
          );
        case "status":
          return (
            <Chip
              className="capitalize"
              color={
                payment.status === PaymentStatus.SUCCESSFUL
                  ? "success"
                  : "danger"
              }
              size="sm"
              variant="flat"
            >
              {payment.status.toLowerCase()}
            </Chip>
          );
        case "amount":
          return (
            <div className="flex w-full justify-between items-center">
              <p className="text-bold text-small">
                ₹{payment.amount.toFixed(2)}
              </p>
              <Button
                variant="ghost"
                size="sm"
                className="border-none"
                onPress={() => navigate(`/admin/sales/payments/${payment._id}`)}
                isIconOnly
                startContent={
                  <BiChevronRight className="text-xl text-slate-400 hover:text-primary" />
                }
              />
              {/* <BiChevronRight
                onClick={() => navigate(`/admin/sales/payments/${payment._id}`)}
                className="text-xl text-slate-400 font-normal hover:cursor-pointer hover:text-primary hover:bg-lightPrimary rounded-full z-10"
              /> */}
            </div>
          );
        default:
          return typeof cellValue === "object"
            ? JSON.stringify(cellValue)
            : cellValue;
      }
    },
    []
  );

  // We can use this later when implementing bulk actions
  // const finalSelectedKeys = useMemo(() => {
  //   if (selectedPaymentKeys === "all") {
  //     return dummyPayments.map((item: PaymentType) => item._id);
  //   }
  //   return Array.from(selectedPaymentKeys.values());
  // }, [selectedPaymentKeys]);

  // Top content for the table (search, filters, etc.)
  const topContent = useMemo(() => {
    return (
      <TopHeader
        columns={columns}
        filterValue={paymentFilterText}
        onOpen={onOpen}
        onSearchChange={setPaymentFilterText}
        setVisibleColumns={(cols) => setPaymentVisibleColumns(cols)}
        visibleColumns={paymentVisibleColumns}
        showItems={{
          columnsToggle: true,
          exportImportButton: false,
          filters: true,
          searchBar: true,
        }}
      />
    );
  }, [paymentFilterText, paymentVisibleColumns, columns]);

  // Top content when rows are selected
  const topSelectedContent = useMemo(() => {
    return (
      <div className="flex gap-x-2 items-center bg-white dark:bg-slate-900 p-2 rounded-md">
        <p>
          {selectedPaymentKeys === "all" // API_CHANGE will need to add the change here
            ? dummyPayments.length
            : selectedPaymentKeys.size}{" "}
          of {dummyPayments.length} Selected
        </p>
        <RxDividerVertical className="text-3xl font-light text-textPlaceHolderLight" />
        <div className="flex gap-x-3">
          <Button
            color="primary"
            radius="full"
            size="sm"
            variant="flat"
            onPress={() => {
              console.log("Bulk action on selected payments");
              // Implement bulk actions here
            }}
          >
            Bulk Actions
          </Button>
        </div>
      </div>
    );
  }, [selectedPaymentKeys]);

  // We'll implement infinite scroll later when connecting to the API
  // For now, use dummy data and default values

  //   const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  //   const { hasMore, loading: isLoading } = useInfiniteQueryScroll({
  //     items: data?.getRibbonsByFilter?.ribbons || [],
  //     totalCount: data?.getRibbonsByFilter?.totalCount || 0,
  //     loading,
  //     fetchMore,
  //     scrollContainerRef, // pass in the ref
  //   });

  return (
    <>
      <div className="flex justify-between items-center w-full mb-5 mt-2">
        <Breadcrumbs>
          <BreadcrumbItem>Dashboard</BreadcrumbItem>
          <BreadcrumbItem>Payments</BreadcrumbItem>
        </Breadcrumbs>
      </div>

      <TopDrawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        drawerHeader="Filters"
        isLoading={false}
        handleAction={handleAction}
      >
        {/* API_CHANGE Payment filters will go here */}
        <div className="p-4">
          <p>Payment filters coming soon</p>
        </div>
      </TopDrawer>

      <div className="flex justify-between items-center w-full mb-5 mt-2">
        <h1>Payments ({dummyPayments.length})</h1>{" "}
        {/*API_CHANGE replace this with the actual count*/}
      </div>

      <TableComponent<PaymentType>
        columns={columns}
        renderCell={renderCell}
        topContent={topContent}
        topSelectedContent={topSelectedContent}
        list={{ items: dummyPayments }} //API_CHANGE replace this with the actual data
        visibleColumns={paymentVisibleColumns}
        isLoading={false}
        hasMore={false}
        selectedKeys={selectedPaymentKeys}
        setSelectedKeys={setSelectedPaymentKeys}
        handleLoadMore={() => console.log("Load more payments")}
        tableClassName="paymentsTable"
      />
    </>
  );
};

export default Payments;
