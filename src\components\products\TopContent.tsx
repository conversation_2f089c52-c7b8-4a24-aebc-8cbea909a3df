import { UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { CiImageOn } from "react-icons/ci";

import { FieldErrors } from "react-hook-form";
import { CustomFileInput, CustomFileInput2 } from "../forms/CustomInput";
import { ProductSchema } from "../../types/productType";
import { Image } from "@heroui/react";
import { BiPlus } from "react-icons/bi";
import VideoPreview from "../forms/VideoPreview";

interface TopContentProps {
	register: UseFormRegister<ProductSchema>;
	errors: FieldErrors<ProductSchema>;
	watch: UseFormWatch<ProductSchema>;
	setFormValue?: UseFormSetValue<ProductSchema>;
}
const TopContent = ({ register, errors, watch, setFormValue }: TopContentProps) => {
	const images = watch("images") as FileList;
	const videos = watch("videos") as FileList;

	// handling the file input here for the videos and the images
	const handleFileChange = (name: keyof ProductSchema, files: FileList) => {
		if (!files) return;

		const dataTransfer = new DataTransfer();

		// Add existing files
		if (name === "images") {
			for (let i = 0; i < images.length; i++) {
				dataTransfer.items.add(images[i]);
			}
		}

		if (name === "videos") {
			for (let i = 0; i < videos.length; i++) {
				dataTransfer.items.add(videos[i]);
			}
		}

		// Add new files
		for (let i = 0; i < files.length; i++) {
			dataTransfer.items.add(files[i]);
		}
		// Set the updated FileList
		if (setFormValue) {
			setFormValue(name, dataTransfer.files);
		}
	};

	return (
		<div className="w-full">
			<div
				className={`${
					images.length > 0 || videos.length > 0
						? "flex flex-wrap items-center  gap-4 h-full"
						: "grid grid-cols-1 md:grid-cols-2 gap-4 "
				}`}
			>
				{images.length > 0 ? (
					Array.from(images).map((file, index) => (
						<div key={index} className="relative w-40 h-40">
							<Image
								src={URL.createObjectURL(file)}
								height={150}
								width={150}
								classNames={{
									img: "object-cover h-40 w-40",
								}}
								alt={`Preview ${index}`}
								className="rounded-md w-40 h-40"
							/>
						</div>
					))
				) : images.length > 0 || videos.length > 0 ? null : (
					<div>
						<CustomFileInput
							register={register}
							Icon={CiImageOn}
							titleText="Add Images"
							name="images"
							accept="image/*"
						/>
						{errors.images ? (
							<small className="px-4 text-danger">{errors.images.message}</small>
						) : null}
					</div>
				)}

				{videos.length > 0 ? (
					Array.from(videos).map((file, index) => (
						<div key={index} className="relative w-40 h-40">
							<VideoPreview src={URL.createObjectURL(file)} />
						</div>
					))
				) : images.length > 0 || videos.length > 0 ? null : (
					<div>
						<CustomFileInput
							register={register}
							Icon={CiImageOn}
							titleText="Add Videos"
							name="videos"
							accept="video/*"
						/>
						{/* {errors.images ? (
              <small className="px-4 text-danger">
                {errors.images.message}
              </small>
            ) : null} */}
					</div>
				)}
				{images.length > 0 || videos.length > 0 ? (
					<div
						className="flex items-center justify-center -mt-2"
						style={{ height: 152, width: 155 }}
					>
						<div className="bg-lightPrimary h-full w-full rounded-md flex flex-col justify-center gap-y-2 p-3 ">
							<CustomFileInput2
								handleFileChange={handleFileChange}
								register={register}
								Icon={BiPlus}
								titleText="Add Images"
								name="images"
							/>
							<CustomFileInput2
								handleFileChange={handleFileChange}
								register={register}
								Icon={BiPlus}
								accept="video/*"
								titleText="Add Videos"
								name="videos"
							/>
						</div>
					</div>
				) : null}
			</div>
		</div>
	);
};

export default TopContent;
