import { useEffect, useState } from "react";
import { useTheme } from "@heroui/use-theme";
import { Button } from "@heroui/react";
import { IoIosSunny } from "react-icons/io";
import { IoMoon } from "react-icons/io5";

const ThemeSwitcher = () => {
  const [mounted, setMounted] = useState(false);
  const isClient = typeof window !== "undefined";

  // Only run useTheme() on the client
  const { theme, setTheme } = isClient
    ? useTheme()
    : { theme: "light", setTheme: () => {} };

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <>
      <Button
        onPress={() => setTheme(theme === "dark" ? "light" : "dark")}
        isIconOnly
        aria-label="Like"
        size="sm"
        className=" bg-grayBackground  rounded text-white"
      >
        {theme === "light" ? (
          <IoIosSunny className="fill-yellow-400 text-tiny" size={20} />
        ) : (
          <IoMoon className="text-primary text-tiny" size={18} />
        )}
      </Button>
    </>
  );
};

export default ThemeSwitcher;
